<!DOCTYPE html>
<html <?php echo Theme::htmlAttributes(); ?>>
<head>
    <meta charset="utf-8">
    <meta
        http-equiv="X-UA-Compatible"
        content="IE=edge"
    >
    <meta
        name="viewport"
        content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=5, user-scalable=1"
    />
    <meta
        name="csrf-token"
        content="<?php echo e(csrf_token()); ?>"
    >


    <!-- Inline script to immediately remove specific Wishlist and Compare buttons -->
    <script>
        // Immediately executing function to remove only specific Wishlist and Compare buttons
        (function() {
            // Function to run immediately
            function removeSpecificButtons() {
                // Only target buttons in the product detail page, not in the header
                var productPage = document.querySelector('.product-detail-container');
                if (productPage) {
                    // Find all Wishlist and Compare links in the product page
                    var productLinks = productPage.querySelectorAll('a');
                    for (var i = 0; i < productLinks.length; i++) {
                        var link = productLinks[i];
                        var text = link.textContent.trim();

                        if (text === 'Wishlist' || text === 'Compare') {
                            // Hide the link
                            link.style.display = 'none';

                            // Also hide parent elements
                            var parent = link.parentElement;
                            if (parent) {
                                parent.style.display = 'none';
                            }
                        }
                    }
                }
            }

            // Run immediately
            removeSpecificButtons();

            // Also run when DOM is loaded
            document.addEventListener('DOMContentLoaded', removeSpecificButtons);

            // Run periodically to catch any dynamically added elements
            setInterval(removeSpecificButtons, 100);
        })();
    </script>

    <!-- Inline style to hide only specific Wishlist and Compare buttons -->
    <style>
        /* Only target buttons in the product detail page, not in the header */
        .product-detail-container a[href*="wishlist"]:not(.product-wishlist-button a),
        .product-detail-container a[href*="compare"],
        .product-detail-container .icon-heart,
        .product-detail-container .icon-repeat,
        .product-detail-container .btn-wishlist,
        .product-detail-container .btn-compare,
        .product-detail-container .compare-button,
        .product-detail-container .wishlist-button:not(.product-wishlist-button) {
            display: none !important;
        }
    </style>

    <style>
        :root {
            --primary-color: <?php echo e(theme_option('primary_color', '#fab528')); ?>;
            --primary-color-rgb: <?php echo e(implode(', ', BaseHelper::hexToRgb(theme_option('primary_color', '#fab528')))); ?>;
            --heading-color: <?php echo e(theme_option('heading_color', '#000')); ?>;
            --text-color: <?php echo e(theme_option('text_color', '#000')); ?>;
            --primary-button-color: <?php echo e(theme_option('primary_button_color', '#000')); ?>;
            --primary-button-background-color: <?php echo e(theme_option('primary_button_background_color') ?: theme_option('primary_color', '#fab528')); ?>;
            --top-header-background-color: <?php echo e(theme_option('top_header_background_color', '#f7f7f7')); ?>;
            --top-header-text-color: <?php echo e(theme_option('top_header_text_color') ?: theme_option('header_text_color', '#000')); ?>;
            --middle-header-background-color: <?php echo e(theme_option('middle_header_background_color', '#fff')); ?>;
            --middle-header-text-color: <?php echo e(theme_option('middle_header_text_color') ?: theme_option('header_text_color', '#000')); ?>;
            --bottom-header-background-color: <?php echo e(theme_option('bottom_header_background_color', '#fff')); ?>;
            --bottom-header-text-color: <?php echo e(theme_option('bottom_header_text_color') ?: theme_option('header_text_color', '#000')); ?>;
            --header-text-color: <?php echo e(theme_option('header_text_color', '#000')); ?>;
            --header-text-secondary-color: <?php echo e(BaseHelper::hexToRgba(theme_option('header_text_color', '#000'), 0.5)); ?>;
            --header-deliver-color: <?php echo e(BaseHelper::hexToRgba(theme_option('header_deliver_color', '#000'), 0.15)); ?>;
            --footer-text-color: <?php echo e(theme_option('footer_text_color', '#555')); ?>;
            --footer-heading-color: <?php echo e(theme_option('footer_heading_color', '#555')); ?>;
            --footer-hover-color: <?php echo e(theme_option('footer_hover_color', '#fab528')); ?>;
            --footer-border-color: <?php echo e(theme_option('footer_border_color', '#dee2e6')); ?>;
        }

        /* ULTIMATE BORDER REMOVAL - Remove border between middle header and bottom header */
        .header .header-middle,
        .header .header-middle.header-content-sticky,
        header.header .header-middle,
        header.header .header-middle.header-content-sticky,
        body .header .header-middle,
        body .header .header-middle.header-content-sticky,
        html body .header .header-middle,
        html body .header .header-middle.header-content-sticky,
        .header-middle,
        .header-middle *,
        .header-middle::before,
        .header-middle::after,
        .header-middle .header-wrapper,
        .header-middle .header-wrapper::before,
        .header-middle .header-wrapper::after {
            border-bottom: none !important;
            border-bottom-width: 0 !important;
            border-bottom-style: none !important;
            border-bottom-color: transparent !important;
            box-shadow: none !important;
            outline: none !important;
            background-image: none !important;
            --header-deliver-color: transparent !important;
        }

        /* Also target the next element after header-middle */
        .header-middle + *,
        .header-middle ~ .header-bottom,
        .header-bottom,
        .header-bottom::before,
        .header-bottom::after {
            border-top: none !important;
            border-top-width: 0 !important;
            border-top-style: none !important;
            border-top-color: transparent !important;
            box-shadow: none !important;
            outline: none !important;
        }

        /* Override CSS variable that controls header border */
        :root {
            --header-deliver-color: transparent !important;
        }

        /* Checkout page fixes for all platforms */
        body.checkout-page {
            background-color: #f8f9fa !important;
        }

        .checkout-content-wrap {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            padding: 20px;
            margin-top: 20px;
            margin-bottom: 20px;
        }

        /* Product list styling */
        .checkout-page .cart-item {
            border-bottom: 1px solid #f1f1f1;
            padding-bottom: 15px;
            margin-bottom: 15px;
        }

        .checkout-page .checkout-product-img-wrapper {
            width: 80px;
            height: 80px;
            overflow: hidden;
            border: 1px solid #f1f1f1;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            position: relative;
        }

        .checkout-page .checkout-product-img-wrapper img {
            object-fit: cover;
            width: 100%;
            height: 100%;
            transition: transform 0.3s ease;
        }

        .checkout-page .checkout-product-img-wrapper:hover img {
            transform: scale(1.05);
        }

        .checkout-page .checkout-quantity {
            position: absolute;
            top: -8px;
            right: -8px;
            background-color: #ff6633;
            color: white;
            border-radius: 50%;
            width: 22px;
            height: 22px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        /* Quantity controls */
        .checkout-page .ec-checkout-quantity {
            border: 1px solid #e0e0e0;
            border-radius: 30px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100px;
            height: 36px;
            margin-top: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }

        .checkout-page .ec-checkout-quantity:hover {
            border-color: #ff6633;
            box-shadow: 0 2px 8px rgba(255, 102, 51, 0.2);
        }

        .checkout-page .ec-checkout-quantity input {
            border: none;
            text-align: center;
            width: 40px;
            padding: 0;
            margin: 0;
            height: 100%;
            font-weight: bold;
            background-color: transparent;
        }

        .checkout-page .ec-checkout-quantity-control {
            cursor: pointer;
            width: 30px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            color: #ff6633;
            transition: all 0.2s ease;
        }

        .checkout-page .ec-checkout-quantity-control:hover {
            background-color: #fff0eb;
        }

        /* Hide the Price: section completely - using standard selectors */
        .checkout-page .small.d-flex.justify-content-between,
        .checkout-page .small + .small,
        .checkout-page small + small,
        .checkout-page div + .small,
        .checkout-page .ec-checkout-quantity + div,
        .checkout-page .ec-checkout-quantity + .small {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            width: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            overflow: hidden !important;
        }

        /* Direct override for product options */
        .checkout-page .small.d-flex.justify-content-between,
        .checkout-page div.small[style*="display:block"],
        .checkout-page div.small[style*="display: block"],
        .checkout-page div.small:first-of-type,
        .checkout-page div.small:first-child {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            width: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            overflow: hidden !important;
        }

        /* EXTREMELY AGGRESSIVE approach to hide the price section */
        /* Target any element that might contain the price */
        .checkout-page div,
        .checkout-page p,
        .checkout-page span,
        .checkout-page small,
        .checkout-page .small,
        .checkout-page [class*="small"],
        .checkout-page [class*="price"],
        .checkout-page [class*="Price"],
        .checkout-page [class*="option"],
        .checkout-page [class*="Option"] {
            /* Will be handled by JavaScript */
        }

        /* Direct targeting of the price section */
        .checkout-page .ec-checkout-quantity ~ div,
        .checkout-page .ec-checkout-quantity ~ p,
        .checkout-page .ec-checkout-quantity ~ span,
        .checkout-page .ec-checkout-quantity ~ small,
        .checkout-page .ec-checkout-quantity ~ .small,
        .checkout-page [data-bb-toggle="update-cart"] ~ div,
        .checkout-page [data-bb-toggle="update-cart"] ~ p,
        .checkout-page [data-bb-toggle="update-cart"] ~ span,
        .checkout-page [data-bb-toggle="update-cart"] ~ small,
        .checkout-page [data-bb-toggle="update-cart"] ~ .small {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            width: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            overflow: hidden !important;
        }

        /* Override for the render-options-html template */
        .checkout-page [data-bb-toggle="update-cart"] ~ div,
        .checkout-page [data-bb-toggle="update-cart"] ~ div.small,
        .checkout-page [data-bb-toggle="update-cart"] ~ .small,
        .checkout-page .ec-checkout-quantity ~ div,
        .checkout-page .ec-checkout-quantity ~ div.small,
        .checkout-page .ec-checkout-quantity ~ .small {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            width: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            overflow: hidden !important;
        }

        /* Price styling */
        .checkout-page .price-text {
            font-weight: bold;
            color: #ff6633;
        }

        .checkout-page .total-text {
            font-size: 1.5rem;
            font-weight: bold;
            color: #ff6633;
        }

        /* Improved price display in checkout */
        .checkout-page .checkout-price-display {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            margin-bottom: 5px;
        }

        .checkout-page .checkout-price-display .sale-price {
            color: #ff6633;
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 2px;
        }

        .checkout-page .checkout-price-display .original-price {
            text-decoration: line-through;
            color: #999;
            font-size: 14px;
        }

        /* Hide Upper Subtotal Section */
        .checkout-page .upper-subtotal-section,
        .checkout-page .mt-2.p-2 > .row:not(.grand-total-section .row) {
            display: none !important;
            visibility: hidden !important;
            height: 0 !important;
            width: 0 !important;
            margin: 0 !important;
            padding: 0 !important;
            overflow: hidden !important;
            position: absolute !important;
            left: -9999px !important;
            opacity: 0 !important;
            pointer-events: none !important;
        }

        /* Grand Total Section Styling */
        .checkout-page .grand-total-section {
            border: 2px solid #003366 !important;
            border-radius: 8px !important;
            padding: 15px !important;
            background-color: #f8f9fa !important;
            margin-top: 20px !important;
            margin-bottom: 10px !important;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
        }

        .checkout-page .grand-total-title {
            color: #003366 !important;
            font-weight: bold !important;
            text-align: center !important;
            margin-top: 0 !important;
            margin-bottom: 15px !important;
            font-size: 18px !important;
            text-transform: uppercase !important;
            letter-spacing: 1px !important;
        }

        .checkout-page .grand-total-section .total-text {
            font-weight: bold !important;
            font-size: 18px !important;
            color: #003366 !important;
            margin-bottom: 0 !important;
        }

        /* Shipping method styling */
        .checkout-page .shipping-method-wrapper {
            border: 1px solid #f1f1f1;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .checkout-page .shipping-method-wrapper .radio-item {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
            transition: all 0.2s;
        }

        .checkout-page .shipping-method-wrapper .radio-item:hover {
            background-color: #f8f9fa;
        }

        /* Checkout button */
        .checkout-page .payment-checkout-btn {
            background-color: #ff6633;
            border-color: #ff6633;
            padding: 10px 20px;
            font-weight: bold;
            border-radius: 5px;
        }

        .checkout-page .payment-checkout-btn:hover {
            background-color: #e55a2d;
            border-color: #e55a2d;
        }

        /* Custom search bar styling for desktop */
        @media (min-width: 769px) {
            .header-middle {
                padding: 0 !important;
                height: 50px !important;
                align-items: center !important;
            }

            .header-middle .header__left {
                width: 15%;
                display: flex;
                align-items: center;
            }

            .header-middle .header__center {
                width: 70%;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .header-middle .header__right {
                width: 15%;
                display: flex;
                justify-content: flex-end;
                align-items: center;
                gap: 15px;
            }

            .header-middle .header__center .form--quick-search {
                border: 2px solid #222222;
                border-radius: 30px;
                width: 100%;
                max-width: 900px;
                margin: 0 auto;
                display: flex;
                align-items: center;
                overflow: hidden;
                position: relative;
                background: white;
            }

            .header-middle .header__center .form--quick-search input {
                border: none;
                padding: 8px 25px;
                height: 38px;
                flex: 1;
                font-size: 14px;
                background: transparent;
            }

            .header-middle .header__center .form--quick-search input:focus {
                outline: none;
                box-shadow: none;
            }

            .header-middle .header__center .form--quick-search .btn {
                background: #ff6633;
                min-width: 70px;
                height: 38px;
                display: flex;
                align-items: center;
                justify-content: center;
                border: none;
                border-radius: 0 28px 28px 0;
                margin-left: -2px;
                transition: background-color 0.2s ease;
                position: relative;
                right: -1px;
            }

            .header-middle .header__center .form--quick-search .btn:hover {
                background:rgb(167, 60, 27);
            }

            .header-middle .header__center .form--quick-search .btn svg {
                fill: #fff;
                width: 22px;
                height: 22px;
            }

            /* Header bottom height reduction */
            .header-bottom {
                padding: 8px 0;
                border-top: 1px solid #eee;
                height: 40px !important;
                min-height: 40px !important;
                max-height: 40px !important;
            }

            .header-bottom .navigation__left {
                width: 20%;
            }

            .header-bottom .navigation__center {
                width: 80%;
            }

            .header-bottom .menu--product-categories .menu__toggle {
                padding: 5px 15px;
                height: 34px;
            }

            .header-bottom .menu--product-categories .menu__toggle-title {
                font-size: 14px;
            }

            .header-bottom .menu .menu__item > a {
                padding: 5px 15px;
                font-size: 14px;
            }

            /* Adjust logo size */
            .header-middle .logo img {
                max-height: 35px;
                width: auto;
            }

            /* Adjust icons size in header right */
            .header-middle .header__right {
                display: flex !important;
                align-items: center !important;
                justify-content: flex-end !important;
                flex-direction: row !important;
                gap: 10px !important;
            }

            .header-middle .header__right .header__extra {
                margin: 0 !important;
                display: flex !important;
                align-items: center !important;
                flex-shrink: 0 !important;
            }

            .header-middle .header__right .header__extra.header-wishlist {
                margin-right: 10px !important;
            }

            .header-middle .header__right .cart--mini {
                display: flex !important;
                align-items: center !important;
                flex-shrink: 0 !important;
            }

            .header-middle .header__right .svg-icon {
                width: 22px !important;
                height: 22px !important;
            }

            .header-middle .header__right .header-item-counter {
                top: 2px !important;
                right: 2px !important;
                width: 18px !important;
                height: 18px !important;
                font-size: 10px !important;
                line-height: 18px !important;
                text-align: center !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                background: #ff6633 !important;
                color: white !important;
                border-radius: 50% !important;
                position: absolute !important;
                z-index: 10 !important;
            }
        }

        /* Reduce breadcrumb height to 20px */
        .page-breadcrumbs {
            padding: 2px 0 !important;
            height: 20px !important;
            min-height: 20px !important;
            max-height: 20px !important;
            line-height: 16px !important;
            display: flex !important;
            align-items: center !important;
        }

        .page-breadcrumbs .breadcrumb {
            margin-bottom: 0 !important;
            padding: 0 !important;
            background: transparent !important;
            font-size: 12px !important;
            line-height: 16px !important;
            height: 16px !important;
            display: flex !important;
            align-items: center !important;
        }

        .page-breadcrumbs .breadcrumb-item {
            font-size: 12px !important;
            line-height: 16px !important;
            height: 16px !important;
            display: flex !important;
            align-items: center !important;
        }

        .page-breadcrumbs .breadcrumb-item a,
        .page-breadcrumbs .breadcrumb-item span {
            font-size: 12px !important;
            line-height: 16px !important;
            height: 16px !important;
            display: flex !important;
            align-items: center !important;
            padding: 0 !important;
            margin: 0 !important;
        }

        /* Hide breadcrumbs on mobile and tablet */
        @media (max-width: 768px) {
            .page-breadcrumbs,
            nav[aria-label="breadcrumb"],
            .breadcrumb,
            .page-header .page-breadcrumbs,
            ol.breadcrumb,
            .breadcrumb-item {
                display: none !important;
                visibility: hidden !important;
                height: 0 !important;
                opacity: 0 !important;
                pointer-events: none !important;
                position: absolute !important;
                left: -9999px !important;
            }

            /* Mobile header styling */
            .header-mobile {
                background-color: #ff6633 !important;
                padding: 10px 15px !important;
                border-bottom: none !important;
            }

            /* Back button styling */
            .header-items-mobile--left .back-button a {
                color: white !important;
                font-size: 20px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            .header-items-mobile--left .back-button .svg-icon svg {
                fill: white !important;
                width: 24px !important;
                height: 24px !important;
            }

            /* Search bar styling */
            .header-items-mobile--center .search-form--mobile {
                width: 100% !important;
            }

            .header-items-mobile--center .form--quick-search {
                width: 100% !important;
                border: none !important;
                border-radius: 30px !important;
                overflow: hidden !important;
            }

            .header-items-mobile--center .form--quick-search input {
                height: 40px !important;
                border-radius: 5px 0 0 5px !important;
                padding: 5px 15px !important;
                background-color: white !important;
                border: none !important;
                width: calc(100% - 40px) !important;
                float: left !important;
            }

            .header-items-mobile--center .form--quick-search .search-wrapper {
                display: flex !important;
                position: relative !important;
                width: 100% !important;
            }

            .header-items-mobile--center .form--quick-search .btn-search {
                width: 40px !important;
                height: 40px !important;
                background-color: white !important;
                border: none !important;
                border-radius: 0 5px 5px 0 !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                padding: 0 !important;
            }

            .header-items-mobile--center .form--quick-search .btn-search .svg-icon {
                width: 20px !important;
                height: 20px !important;
            }

            .header-items-mobile--center .form--quick-search .btn-search .svg-icon svg {
                fill:#ff6633 !important;
            }

            /* Menu icon styling */
            .header-items-mobile--right .menu-mobile .menu-icon {
                color: white !important;
            }

            .header-items-mobile--right .menu-mobile .svg-icon svg {
                fill: white !important;
                width: 24px !important;
                height: 24px !important;
            }

            /* Adjust widths for the three sections */
            .header-items-mobile--left {
                width: 15% !important;
                display: flex !important;
                align-items: center !important;
                justify-content: flex-start !important;
            }

            .header-items-mobile--center {
                width: 70% !important;
            }

            .header-items-mobile--right {
                width: 15% !important;
                display: flex !important;
                align-items: center !important;
                justify-content: flex-end !important;
            }

            /* Homepage specific styling */
            body.home .header-items-mobile--left .menu-mobile .menu-icon {
                color: white !important;
            }

            body.home .header-items-mobile--left .menu-mobile .svg-icon svg {
                fill: white !important;
                width: 24px !important;
                height: 24px !important;
            }

            /* Hide grid/list view selection in mobile and tablet */
            .catalog-toolbar__view .toolbar-view__icon,
            .store-toolbar__view .toolbar-view__icon,
            .catalog-toolbar__view .d-flex,
            .store-toolbar__view .d-flex {
                display: none !important;
            }

            /* Mobile-specific checkout fixes */

            /* Responsive checkout fixes for mobile */
            .checkout-page .order-1 {
                order: 1 !important;
            }

            .checkout-page .order-md-2 {
                order: 2 !important;
            }

            .checkout-page .payment-checkout-btn {
                width: 100%;
                margin-top: 15px;
            }

            .checkout-page .ec-checkout-quantity {
                width: 90px;
            }
        }

        /* Header height reduction styles */
        .header-middle {
            padding: 0 !important;
            height: 70px !important;
            min-height: 70px !important;
            max-height: 70px !important;
            align-items: center !important;
            display: flex !important;
        }

        .header-middle .header-wrapper {
            display: flex !important;
            align-items: center !important;
            height: 70px !important;
            min-height: 70px !important;
            max-height: 70px !important;
            padding: 0 !important;
        }

        .header-middle .header__left {
            width: 15% !important;
        }

        .header-middle .header__center {
            width: 70% !important;
        }

        .header-middle .header__right {
            width: 15% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: flex-end !important;
            flex-direction: row !important;
            gap: 10px !important;
        }

        .header-middle .header__center .form--quick-search {
            transition: all 0.3s ease !important;
        }

        .header-middle .header__center .form--quick-search:hover {
            transform: scale(1.01) !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        }

        .header-middle .header__center .form--quick-search input {
            padding: 10px 25px !important;
            height: 44px !important;
        }

        .header-middle .header__center .form--quick-search .btn {
            height: 44px !important;
            min-width: 70px !important;
            background: #ff6633 !important;
        }

        .header-bottom {
            padding: 2px 10px 0 0 !important;
            border-top: 0px solid #ffffff !important;
            height: 50px !important;
            margin-bottom: 20px !important;
        }

        .header-bottom .menu--product-categories .menu__toggle {
            padding: 0px 2px !important;
            height: 12px !important;
            font-size: 13px !important;
            display: flex !important;
            align-items: center !important;
            background: transparent !important;
        }

        .header-bottom .menu--product-categories .menu__toggle .svg-icon {
            width: 22px !important;
            height: 22px !important;
            margin-right: 5px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        .header-bottom .menu .menu__item > a {
            padding: 2px 15px !important;
            font-size: 13px !important;
            height: 28px !important;
            line-height: 28px !important;
        }

        .header-middle .logo img {
            max-height: 50px !important;
            width: auto !important;
        }

        .header-middle .header__right .svg-icon {
            width: 22px !important;
            height: 22px !important;
        }

        /* Force horizontal layout for header extra elements */
        .header-middle .header__right .header__extra {
            display: inline-flex !important;
            align-items: center !important;
            flex-shrink: 0 !important;
            margin: 0 5px !important;
            vertical-align: middle !important;
        }

        .header-middle .header__right .cart--mini {
            display: inline-flex !important;
            align-items: center !important;
            flex-shrink: 0 !important;
            margin: 0 5px !important;
            vertical-align: middle !important;
        }

        /* Fix nested header__extra in cart */
        .header-middle .header__right .cart--mini .header__extra {
            display: inline-flex !important;
            align-items: center !important;
            margin: 0 !important;
            vertical-align: middle !important;
        }

        /* Ensure wishlist and cart are side by side */
        .header-middle .header__right .header-wishlist {
            display: inline-flex !important;
            align-items: center !important;
            margin-right: 10px !important;
            vertical-align: middle !important;
        }

        /* FORCE HORIZONTAL LAYOUT - Desktop header right section */
        @media (min-width: 993px) {
            /* Force container to be horizontal */
            .header .header-middle .header__right {
                display: flex !important;
                flex-direction: row !important;
                align-items: center !important;
                justify-content: flex-end !important;
                gap: 15px !important;
                flex-wrap: nowrap !important;
                height: 70px !important;
                min-height: 70px !important;
                max-height: 70px !important;
                width: auto !important;
            }

            /* Force all direct children to be inline */
            .header .header-middle .header__right > .header__extra {
                display: inline-block !important;
                vertical-align: middle !important;
                float: left !important;
                margin-right: 15px !important;
                margin-left: 0 !important;
                margin-top: 0 !important;
                margin-bottom: 0 !important;
                clear: none !important;
                position: relative !important;
                width: auto !important;
                height: auto !important;
            }

            /* Specific targeting for wishlist */
            .header .header-middle .header__right .header__extra.header-wishlist {
                display: inline-block !important;
                vertical-align: middle !important;
                float: left !important;
                margin-right: 15px !important;
                margin-left: 0 !important;
                margin-top: 0 !important;
                margin-bottom: 0 !important;
                clear: none !important;
                position: relative !important;
                width: auto !important;
                height: auto !important;
            }

            /* Specific targeting for cart */
            .header .header-middle .header__right .header__extra.cart--mini {
                display: inline-block !important;
                vertical-align: middle !important;
                float: left !important;
                margin-right: 0 !important;
                margin-left: 0 !important;
                margin-top: 0 !important;
                margin-bottom: 0 !important;
                clear: none !important;
                position: relative !important;
                width: auto !important;
                height: auto !important;
            }

            /* Fix nested cart structure */
            .header .header-middle .header__right .cart--mini .header__extra {
                display: inline-block !important;
                vertical-align: middle !important;
                float: none !important;
                margin: 0 !important;
                clear: none !important;
                position: relative !important;
                width: auto !important;
                height: auto !important;
            }

            /* Button styling */
            .header .header-middle .header__right .header__extra a {
                display: inline-block !important;
                vertical-align: middle !important;
                position: relative !important;
                padding: 10px !important;
                margin: 0 !important;
                text-decoration: none !important;
            }

            .header .header-middle .header__right .btn-wishlist,
            .header .header-middle .header__right .btn-shopping-cart {
                display: inline-block !important;
                vertical-align: middle !important;
                position: relative !important;
                padding: 10px !important;
                margin: 0 !important;
                text-decoration: none !important;
            }

            /* Icon styling */
            .header .header-middle .header__right .svg-icon {
                display: inline-block !important;
                vertical-align: middle !important;
                width: 24px !important;
                height: 24px !important;
                margin: 0 !important;
            }

            /* Counter positioning */
            .header .header-middle .header__right .header-item-counter {
                position: absolute !important;
                top: 2px !important;
                right: 2px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                width: 18px !important;
                height: 18px !important;
                font-size: 10px !important;
                line-height: 1 !important;
                text-align: center !important;
                background: #ff6633 !important;
                color: white !important;
                border-radius: 50% !important;
                z-index: 10 !important;
            }

            /* Clear any floats after the header right section */
            .header .header-middle .header__right::after {
                content: "" !important;
                display: table !important;
                clear: both !important;
            }

            /* ULTIMATE OVERRIDE - Force horizontal layout with perfect vertical centering */
            .header-middle .header-wrapper .header__right .header__extra.header-wishlist,
            .header-middle .header-wrapper .header__right .header__extra.cart--mini {
                display: inline-block !important;
                vertical-align: middle !important;
                float: left !important;
                margin-right: 15px !important;
                margin-top: 0 !important;
                margin-bottom: 0 !important;
                margin-left: 0 !important;
                clear: none !important;
                width: auto !important;
                height: 70px !important;
                line-height: 70px !important;
                position: relative !important;
            }

            /* Remove any block display that might cause stacking */
            .header-middle .header__right .header__extra {
                display: inline-block !important;
                vertical-align: middle !important;
                float: left !important;
                clear: none !important;
                margin-right: 10px !important;
                margin-top: 0 !important;
                margin-bottom: 0 !important;
                width: auto !important;
                height: 70px !important;
                line-height: 70px !important;
            }

            /* Ensure cart doesn't stack */
            .header-middle .header__right .cart--mini {
                display: inline-block !important;
                vertical-align: middle !important;
                float: left !important;
                clear: none !important;
                margin-right: 0 !important;
                margin-top: 0 !important;
                margin-bottom: 0 !important;
                width: auto !important;
                height: 70px !important;
                line-height: 70px !important;
            }

            /* Perfect vertical centering for buttons and links */
            .header-middle .header__right .header__extra a,
            .header-middle .header__right .btn-wishlist,
            .header-middle .header__right .btn-shopping-cart {
                display: inline-block !important;
                vertical-align: middle !important;
                height: 70px !important;
                line-height: 70px !important;
                padding: 0 10px !important;
                margin: 0 !important;
                text-decoration: none !important;
                position: relative !important;
            }

            /* Center the SVG icons within the buttons */
            .header-middle .header__right .svg-icon {
                display: inline-block !important;
                vertical-align: middle !important;
                width: 24px !important;
                height: 24px !important;
                margin: 0 !important;
                line-height: 1 !important;
            }

            /* Adjust counter positioning for better alignment */
            .header-middle .header__right .header-item-counter {
                position: absolute !important;
                top: 2px !important;
                right: 2px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                width: 18px !important;
                height: 18px !important;
                font-size: 10px !important;
                line-height: 1 !important;
                text-align: center !important;
                background: #ff6633 !important;
                color: white !important;
                border-radius: 50% !important;
                z-index: 10 !important;
                font-weight: bold !important;
            }
        }

        /* Modern category button with 30px radius and custom color */
        .menu--product-categories {
            border-radius: 30px !important;
            background-color: #f5ebe4 !important;
        }

        /* Fix Product List View Design */
        .shop-products-listing__list {
            gap: 15px !important;
        }

        .shop-products-listing__list .product-inner {
            display: flex !important;
            align-items: stretch !important;
            background: #fff !important;
            border: 1px solid #e8e8e8 !important;
            border-radius: 8px !important;
            padding: 25px !important;
            margin-bottom: 20px !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
            transition: all 0.3s ease !important;
            min-height: 160px !important;
        }

        .shop-products-listing__list .product-inner:hover {
            border-color: #ff6633 !important;
            box-shadow: 0 4px 16px rgba(255, 102, 51, 0.15) !important;
            transform: translateY(-2px) !important;
        }

        /* Hide the orange price overlay in list view */
        .shop-products-listing__list .mobile-price-tag {
            display: none !important;
        }

        .shop-products-listing__list .product-thumbnail {
            flex-basis: 160px !important;
            width: 160px !important;
            min-width: 160px !important;
            margin-bottom: 0 !important;
            margin-right: 25px !important;
            border-radius: 8px !important;
            overflow: hidden !important;
        }

        .shop-products-listing__list .product-thumbnail .img-fluid-eq {
            border-radius: 8px !important;
            overflow: hidden !important;
            height: 120px !important;
        }

        .shop-products-listing__list .product-thumbnail img {
            width: 100% !important;
            height: 120px !important;
            object-fit: cover !important;
            border-radius: 8px !important;
        }

        .shop-products-listing__list .product-details {
            flex: 1 !important;
            display: flex !important;
            flex-direction: column !important;
            justify-content: flex-start !important;
            padding: 0 !important;
            min-height: 120px !important;
        }

        .shop-products-listing__list .product-content-box {
            flex: 1 !important;
            padding: 0 !important;
            display: flex !important;
            flex-direction: column !important;
            justify-content: flex-start !important;
        }

        .shop-products-listing__list .sold-by-meta {
            margin-bottom: 8px !important;
            order: 1 !important;
        }

        .shop-products-listing__list .sold-by-meta a {
            font-size: 10px !important;
            padding: 3px 8px !important;
            border-radius: 3px !important;
            display: inline-block !important;
            font-weight: 500 !important;
        }

        .shop-products-listing__list .product__title {
            font-size: 16px !important;
            font-weight: 600 !important;
            line-height: 1.3 !important;
            margin-bottom: 8px !important;
            color: #333 !important;
            order: 2 !important;
        }

        .shop-products-listing__list .product__title a {
            color: #333 !important;
            text-decoration: none !important;
        }

        .shop-products-listing__list .product__title a:hover {
            color: #ff6633 !important;
        }

        .shop-products-listing__list .star-rating {
            margin-bottom: 10px !important;
            order: 3 !important;
        }

        /* Force desktop price to show in list view */
        .shop-products-listing__list .d-none.d-md-block {
            display: block !important;
        }

        .shop-products-listing__list .product-price {
            margin-top: auto !important;
            padding-top: 10px !important;
            order: 4 !important;
        }

        .shop-products-listing__list .product-price .price-text {
            font-size: 20px !important;
            font-weight: 700 !important;
            color: #ff6633 !important;
        }

        .shop-products-listing__list .product-price .price-original {
            font-size: 16px !important;
            color: #999 !important;
            text-decoration: line-through !important;
            margin-left: 8px !important;
        }

        .shop-products-listing__list .product-bottom-box {
            display: none !important;
        }

        .shop-products-listing__list .ribbons {
            position: absolute !important;
            top: 10px !important;
            left: 10px !important;
            z-index: 2 !important;
        }

        .shop-products-listing__list .ribbon {
            font-size: 11px !important;
            padding: 4px 8px !important;
            border-radius: 3px !important;
        }

        /* Mobile responsive for list view */
        @media (max-width: 768px) {
            .shop-products-listing__list .product-inner {
                flex-direction: column !important;
                min-height: auto !important;
                padding: 15px !important;
            }

            .shop-products-listing__list .product-thumbnail {
                flex-basis: auto !important;
                width: 100% !important;
                min-width: auto !important;
                margin-right: 0 !important;
                margin-bottom: 15px !important;
            }

            .shop-products-listing__list .product-thumbnail .img-fluid-eq {
                height: 200px !important;
            }

            .shop-products-listing__list .product-thumbnail img {
                height: 200px !important;
            }

            .shop-products-listing__list .product-details {
                min-height: auto !important;
            }
        }

        /* Cart Sidebar Styles */
        .panel--sidebar {
            position: fixed !important;
            top: 0 !important;
            right: -400px !important;
            width: 400px !important;
            height: 100vh !important;
            background: #ffffff !important;
            z-index: 999999 !important;
            transition: right 0.3s ease !important;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1) !important;
            overflow-y: auto !important;
        }

        .panel--sidebar.active {
            right: 0 !important;
        }

        .panel--sidebar__right {
            right: -400px !important;
        }

        .panel--sidebar__right.active {
            right: 0 !important;
        }

        .panel__content {
            padding: 20px !important;
            height: 100% !important;
            display: flex !important;
            flex-direction: column !important;
        }

        .panel__header {
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            padding-bottom: 15px !important;
            border-bottom: 1px solid #eee !important;
            margin-bottom: 20px !important;
        }

        .panel__header h4 {
            margin: 0 !important;
            font-size: 18px !important;
            font-weight: 600 !important;
            color: #333 !important;
        }

        .btn-close-sidebar {
            background: none !important;
            border: none !important;
            padding: 5px !important;
            cursor: pointer !important;
            color: #666 !important;
            font-size: 20px !important;
        }

        .btn-close-sidebar:hover {
            color: #333 !important;
        }

        .mini-cart-content {
            flex: 1 !important;
            overflow-y: auto !important;
        }

        /* Sidebar overlay */
        .sidebar-overlay {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background: rgba(0, 0, 0, 0.5) !important;
            z-index: 999998 !important;
            opacity: 0 !important;
            visibility: hidden !important;
            transition: all 0.3s ease !important;
        }

        .sidebar-overlay.active {
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* Mobile cart sidebar adjustments */
        @media (max-width: 992px) {
            .panel--sidebar {
                width: 100% !important;
                right: -100% !important;
                z-index: 999999 !important;
            }

            .panel--sidebar__right {
                right: -100% !important;
                z-index: 999999 !important;
            }

            .panel--sidebar.active,
            .panel--sidebar__right.active {
                right: 0 !important;
            }

            /* Ensure cart sidebar appears above mobile header */
            #cart-mobile {
                z-index: 999999 !important;
            }

            /* Mobile cart header adjustments */
            .panel__header {
                padding-top: 70px !important;
            }
        }

        /* Force cart sidebar to be visible and functional */
        .cart--mini .panel--sidebar {
            display: block !important;
            visibility: visible !important;
        }

        /* Ensure cart button is clickable */
        .btn-shopping-cart {
            cursor: pointer !important;
            pointer-events: auto !important;
        }

    </style>

    <?php
        Theme::asset()->remove('language-css');
        Theme::asset()
            ->container('footer')
            ->remove('language-public-js');
        Theme::asset()
            ->container('footer')
            ->remove('simple-slider-owl-carousel-css');
        Theme::asset()
            ->container('footer')
            ->remove('simple-slider-owl-carousel-js');
        Theme::asset()
            ->container('footer')
            ->remove('simple-slider-css');
        Theme::asset()
            ->container('footer')
            ->remove('simple-slider-js');
    ?>

    <?php echo Theme::header(); ?>

    <link rel="stylesheet" href="<?php echo e(asset('themes/farmart/css/product-text-colors.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('themes/farmart/css/disable-hover.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('themes/farmart/css/mobile-spacing-fix.css')); ?>">
    <link rel="stylesheet" href="<?php echo e(asset('themes/farmart/css/search-price-fix.css')); ?>">

    <!-- Inline style to immediately disable hover functionality, fix mobile spacing, and fix doubled prices -->
    <style>
        /* FIX DOUBLED PRICES IN SEARCH RESULTS */
        .product-inner .product-price:not(:first-of-type),
        .product-inner .bb-product-price:not(:first-of-type),
        .product-inner .price:not(:first-of-type),
        .product-inner .price-amount:not(:first-of-type),
        .product-inner [class*="price"]:not(:first-of-type),
        .product-inner .product-price ~ .product-price,
        .product-inner .bb-product-price ~ .bb-product-price,
        .product-inner .price ~ .price,
        .product-inner .price-amount ~ .price-amount,
        .product-inner .product-info .product-price:nth-of-type(n+2),
        .product-inner .product-info .bb-product-price:nth-of-type(n+2),
        .product-inner .product-info .price:nth-of-type(n+2),
        .product-inner .product-info .price-amount:nth-of-type(n+2),
        .col-lg-4 .product-inner .product-price:nth-of-type(n+2),
        .col-md-4 .product-inner .product-price:nth-of-type(n+2),
        .col-4 .product-inner .product-price:nth-of-type(n+2) {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            position: absolute !important;
            left: -9999px !important;
            z-index: -999 !important;
        }

        /* MOBILE SPACING FIX */
        @media (max-width: 768px) {
            body {
                padding-bottom: 120px !important;
            }

            footer.footer {
                margin-top: 100px !important;
            }

            #main-content::after {
                content: '';
                display: block;
                height: 120px;
                width: 100%;
                clear: both;
            }
        }

        /* HIDE ALL PRODUCT BOTTOM BOXES - CONTAINS ADD TO CART */
        .product-inner .product-bottom-box,
        .products .product-inner .product-bottom-box,
        .product-inner:hover .product-bottom-box,
        .products .product-inner:hover .product-bottom-box,
        .product-bottom-box,
        *[class*="product"] .product-bottom-box,
        *[class*="product"]:hover .product-bottom-box,

        /* HIDE ALL PRODUCT LOOP BUTTONS */
        .product-inner .product-loop__buttons,
        .products .product-inner .product-loop__buttons,
        .product-inner:hover .product-loop__buttons,
        .products .product-inner:hover .product-loop__buttons,
        .product-loop__buttons,
        *[class*="product"] .product-loop__buttons,
        *[class*="product"]:hover .product-loop__buttons {
            display: none !important;
            opacity: 0 !important;
            visibility: hidden !important;
            pointer-events: none !important;
            width: 0 !important;
            height: 0 !important;
            position: absolute !important;
            left: -9999px !important;
            z-index: -999 !important;
            max-height: 0 !important;
            overflow: hidden !important;
            clip: rect(0, 0, 0, 0) !important;
            margin: 0 !important;
            padding: 0 !important;
            border: 0 !important;
        }
    </style>

    <!-- Inline script to immediately disable hover functionality -->
    <script>
        // Immediately executing function to disable hover
        (function() {
            // Create a style element
            const style = document.createElement('style');
            style.textContent = `
                /* HIDE ALL PRODUCT BOTTOM BOXES AND BUTTONS */
                .product-inner .product-bottom-box,
                .product-inner:hover .product-bottom-box,
                .product-inner .product-loop__buttons,
                .product-inner:hover .product-loop__buttons,
                .product-bottom-box,
                .product-loop__buttons {
                    display: none !important;
                    opacity: 0 !important;
                    visibility: hidden !important;
                    pointer-events: none !important;
                    width: 0 !important;
                    height: 0 !important;
                    position: absolute !important;
                    left: -9999px !important;
                    z-index: -999 !important;
                }
            `;
            document.head.appendChild(style);

            // Function to run when DOM is loaded
            function disableHover() {
                // Hide all product bottom boxes and loop buttons
                const elements = document.querySelectorAll('.product-bottom-box, .product-loop__buttons');
                elements.forEach(function(element) {
                    element.style.display = 'none';
                    element.style.opacity = '0';
                    element.style.visibility = 'hidden';
                    element.style.pointerEvents = 'none';
                });
            }

            // Run when DOM is loaded
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', disableHover);
            } else {
                disableHover();
            }

            // Run periodically
            setInterval(disableHover, 500);
        })();
    </script>

    <script src="<?php echo e(asset('themes/farmart/js/disable-hover.js')); ?>" defer></script>
    <script src="<?php echo e(asset('themes/farmart/js/price-fix.js')); ?>" defer></script>

    <!-- Cart Sidebar JavaScript - Default Implementation -->
    <script>
        // Working cart sidebar - debug and fix
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Setting up working cart sidebar...');

            // Debug: Check if cart elements exist
            const cartSidebar = document.getElementById('cart-mobile');
            const cartButtons = document.querySelectorAll('.toggle--sidebar[href="#cart-mobile"], a[href="#cart-mobile"], .btn-shopping-cart');

            console.log('Cart sidebar found:', !!cartSidebar);
            console.log('Cart buttons found:', cartButtons.length);

            if (cartButtons.length > 0) {
                cartButtons.forEach((btn, index) => {
                    console.log(`Cart button ${index}:`, btn.className, btn.getAttribute('href'));
                });
            }

            // Ensure cart starts in closed state - force it multiple times
            if (cartSidebar) {
                cartSidebar.classList.remove('active');
                console.log('Cart sidebar initialized in closed state');

                // Force it again after a delay in case something else adds the class
                setTimeout(() => {
                    cartSidebar.classList.remove('active');
                    console.log('Cart sidebar forced closed again');
                    console.log('Cart has active class after force:', cartSidebar.classList.contains('active'));
                }, 500);

                // And again after page fully loads
                setTimeout(() => {
                    cartSidebar.classList.remove('active');
                    console.log('Cart sidebar final force closed');
                }, 1000);
            }

            // Wait for jQuery to load
            const waitForJQuery = setInterval(function() {
                if (window.$) {
                    clearInterval(waitForJQuery);

                    // Universal cart button handler - catch ALL possible cart button clicks
                    $(document).on('click', '.toggle--sidebar, .btn-shopping-cart, a[href="#cart-mobile"], [data-bb-toggle="cart-sidebar"]', function(e) {
                        const href = $(this).attr('href');
                        const toggle = $(this).attr('data-bb-toggle');

                        // Only handle cart-related clicks
                        if (href === '#cart-mobile' || toggle === 'cart-sidebar' || $(this).hasClass('btn-shopping-cart')) {
                            e.preventDefault();
                            console.log('Cart button clicked:', this.className, href, toggle);

                            const $cartSidebar = $('#cart-mobile');
                            console.log('Cart sidebar element found:', $cartSidebar.length > 0);

                            if ($cartSidebar.length > 0) {
                                // Check the cart sidebar's active state, not the button's
                                const isCartOpen = $cartSidebar.hasClass('active');
                                console.log('Cart is currently open:', isCartOpen);

                                if (isCartOpen) {
                                    // Close cart
                                    $cartSidebar.removeClass('active');
                                    $(this).removeClass('active'); // Remove active from button too
                                    $('body').removeClass('sidebar-open');
                                    console.log('Cart closed');
                                } else {
                                    // Open cart - force it to be visible
                                    $cartSidebar.addClass('active');
                                    $(this).addClass('active'); // Add active to button too
                                    $('body').addClass('sidebar-open');

                                    // Force CSS to ensure visibility
                                    $cartSidebar.css({
                                        'transform': 'translateX(0) !important',
                                        'visibility': 'visible !important',
                                        'opacity': '1 !important'
                                    });

                                    console.log('Cart opened and forced visible');

                                    // Debug: Check if active class was added
                                    setTimeout(() => {
                                        console.log('Cart has active class:', $cartSidebar.hasClass('active'));
                                        console.log('Cart computed styles:', {
                                            transform: $cartSidebar.css('transform'),
                                            display: $cartSidebar.css('display'),
                                            visibility: $cartSidebar.css('visibility')
                                        });
                                    }, 100);
                                }
                            } else {
                                console.log('Cart sidebar element not found!');
                            }
                        }
                    });

                    // Close cart functionality
                    $(document).on('click', '.close-toggle--sidebar, .btn-close-sidebar', function(e) {
                        e.preventDefault();
                        const $panel = $(this).closest('.panel--sidebar');
                        $panel.removeClass('active');
                        $('body').removeClass('sidebar-open');
                        console.log('Cart closed via close button');
                    });

                    console.log('Working cart sidebar enabled');
                }
            }, 100);
        });

        // NEW WORKING CART SIDEBAR - GUARANTEED TO WORK
        document.addEventListener('DOMContentLoaded', function() {
            // Create a new working cart sidebar
            const cartHTML = `
                <div id="working-cart-sidebar" style="
                    position: fixed;
                    top: 0;
                    right: -400px;
                    width: 400px;
                    height: 100vh;
                    background: white;
                    z-index: 99999;
                    transition: right 0.3s ease;
                    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
                    overflow-y: auto;
                ">
                    <div style="padding: 20px;">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; border-bottom: 1px solid #eee; padding-bottom: 15px;">
                            <h3 style="margin: 0; color: #333;">Shopping Cart</h3>
                            <button id="close-cart" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #666;">&times;</button>
                        </div>
                        <div id="cart-content-area">
                            <!-- Cart content will be loaded here -->
                        </div>
                    </div>
                </div>
                <div id="cart-overlay" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.5);
                    z-index: 99998;
                    opacity: 0;
                    visibility: hidden;
                    transition: all 0.3s ease;
                "></div>
            `;

            document.body.insertAdjacentHTML('beforeend', cartHTML);

            const workingCart = document.getElementById('working-cart-sidebar');
            const overlay = document.getElementById('cart-overlay');
            const closeBtn = document.getElementById('close-cart');

            // Open cart function
            function openCart() {
                // Load cart content from the original cart
                loadCartContent();

                workingCart.style.right = '0px';
                overlay.style.opacity = '1';
                overlay.style.visibility = 'visible';
                document.body.style.overflow = 'hidden';
                console.log('NEW CART OPENED SUCCESSFULLY!');
            }

            // Load cart content from the existing cart system
            function loadCartContent() {
                const originalCart = document.getElementById('cart-mobile');
                const cartContentArea = document.getElementById('cart-content-area');

                if (originalCart && cartContentArea) {
                    // Copy the content from the original cart
                    const originalContent = originalCart.querySelector('.mini-cart-content, .widget-shopping-cart-content');

                    if (originalContent) {
                        // Clone the content and clean it up
                        const clonedContent = originalContent.cloneNode(true);

                        // Remove any conflicting styles and duplicate headers
                        clonedContent.style.cssText = '';
                        clonedContent.style.padding = '0';
                        clonedContent.style.margin = '0';

                        // Remove duplicate headers
                        const duplicateHeaders = clonedContent.querySelectorAll('.panel__header');
                        duplicateHeaders.forEach(header => header.remove());

                        // Clean up any nested elements
                        const allElements = clonedContent.querySelectorAll('*');
                        allElements.forEach(el => {
                            el.style.transform = '';
                            el.style.position = '';
                            el.style.right = '';
                            el.style.left = '';
                            el.style.top = '';
                            el.style.bottom = '';
                            el.style.background = '';
                            el.style.backgroundColor = '';
                        });

                        // Style the cart items properly
                        const cartItems = clonedContent.querySelectorAll('.mini-cart-item, .cart-item, li');
                        cartItems.forEach(item => {
                            item.style.cssText = `
                                display: flex !important;
                                align-items: flex-start !important;
                                padding: 15px 0 !important;
                                border-bottom: 1px solid #eee !important;
                                background: transparent !important;
                                list-style: none !important;
                            `;
                        });

                        // Style product image containers
                        const imageContainers = clonedContent.querySelectorAll('.product-image, .cart-item-image');
                        imageContainers.forEach(container => {
                            container.style.cssText = `
                                flex-shrink: 0 !important;
                                width: 60px !important;
                                height: 60px !important;
                                margin-right: 12px !important;
                                overflow: hidden !important;
                                border-radius: 4px !important;
                            `;
                        });

                        // Style product images
                        const productImages = clonedContent.querySelectorAll('img');
                        productImages.forEach(img => {
                            // Make sure image is visible and properly sized
                            img.style.cssText = `
                                width: 60px !important;
                                height: 60px !important;
                                object-fit: cover !important;
                                border-radius: 4px !important;
                                display: block !important;
                                visibility: visible !important;
                                opacity: 1 !important;
                            `;

                            // Ensure image loads properly
                            if (!img.src || img.src === '') {
                                img.src = img.getAttribute('data-src') || img.getAttribute('data-lazy') || '/themes/farmart/images/placeholder.png';
                            }
                        });

                        // Style product content (text area)
                        const productContents = clonedContent.querySelectorAll('.product-content, .cart-item-content');
                        productContents.forEach(content => {
                            content.style.cssText = `
                                flex: 1 !important;
                                display: flex !important;
                                flex-direction: column !important;
                                justify-content: flex-start !important;
                                padding-right: 40px !important;
                            `;
                        });

                        // Style product names
                        const productNames = clonedContent.querySelectorAll('.product-name, .cart-item-name, .product-title');
                        productNames.forEach(name => {
                            name.style.cssText = `
                                font-size: 14px !important;
                                font-weight: 500 !important;
                                color: #333 !important;
                                margin-bottom: 5px !important;
                                line-height: 1.4 !important;
                            `;
                        });

                        // Style product prices
                        const productPrices = clonedContent.querySelectorAll('.product-price, .cart-item-price, .price');
                        productPrices.forEach(price => {
                            price.style.cssText = `
                                font-size: 14px !important;
                                font-weight: 600 !important;
                                color: #ff6633 !important;
                                margin-bottom: 3px !important;
                            `;
                        });

                        // Style quantity info
                        const quantities = clonedContent.querySelectorAll('.quantity, .qty');
                        quantities.forEach(qty => {
                            qty.style.cssText = `
                                font-size: 12px !important;
                                color: #666 !important;
                            `;
                        });

                        // Style cart totals
                        const cartTotals = clonedContent.querySelectorAll('.cart-total, .mini-cart-total, .cart-summary');
                        cartTotals.forEach(total => {
                            total.style.cssText = `
                                padding: 15px 0 !important;
                                border-top: 1px solid #eee !important;
                                margin-top: 15px !important;
                                background: transparent !important;
                            `;
                        });

                        // Style buttons
                        const buttons = clonedContent.querySelectorAll('button, .btn, a.btn');
                        buttons.forEach(btn => {
                            if (btn.textContent.includes('Checkout') || btn.classList.contains('checkout')) {
                                btn.style.cssText = `
                                    background: #ff6633 !important;
                                    color: white !important;
                                    border: none !important;
                                    padding: 12px 20px !important;
                                    border-radius: 4px !important;
                                    font-weight: 500 !important;
                                    cursor: pointer !important;
                                    width: 100% !important;
                                    margin-top: 10px !important;
                                `;
                            } else if (btn.textContent.includes('View Cart')) {
                                btn.style.cssText = `
                                    background: transparent !important;
                                    color: #333 !important;
                                    border: 1px solid #ddd !important;
                                    padding: 12px 20px !important;
                                    border-radius: 4px !important;
                                    cursor: pointer !important;
                                    width: 100% !important;
                                    margin-top: 10px !important;
                                `;
                            }
                        });

                        cartContentArea.innerHTML = '';
                        cartContentArea.appendChild(clonedContent);

                        console.log('Cart content loaded and styled');
                    } else {
                        // Fallback: Load via AJAX
                        loadCartViaAjax();
                    }
                } else {
                    // Fallback: Load via AJAX
                    loadCartViaAjax();
                }
            }

            // Load cart content via AJAX
            function loadCartViaAjax() {
                const cartContentArea = document.getElementById('cart-content-area');

                // Show loading state
                cartContentArea.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">Loading cart...</div>';

                // Try to fetch cart content
                fetch('/cart/mini', {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    }
                })
                .then(response => response.text())
                .then(html => {
                    cartContentArea.innerHTML = html;
                    console.log('Cart content loaded via AJAX');
                })
                .catch(error => {
                    console.log('AJAX failed, showing empty cart');
                    cartContentArea.innerHTML = `
                        <div style="color: #666; text-align: center; padding: 40px 20px;">
                            <p>Your cart is empty</p>
                            <p style="font-size: 14px;">Add some products to get started!</p>
                        </div>
                    `;
                });
            }

            // Close cart function
            function closeCart() {
                workingCart.style.right = '-400px';
                overlay.style.opacity = '0';
                overlay.style.visibility = 'hidden';
                document.body.style.overflow = '';
                console.log('NEW CART CLOSED');
            }

            // Cart button click handler
            document.addEventListener('click', function(e) {
                if (e.target.closest('.btn-shopping-cart, .toggle--sidebar[href="#cart-mobile"], a[href="#cart-mobile"]')) {
                    e.preventDefault();
                    e.stopPropagation();
                    openCart();
                }
            }, true);

            // Handle cart interactions (remove items, update quantities, etc.)
            document.addEventListener('click', function(e) {
                const cartContentArea = document.getElementById('cart-content-area');
                if (!cartContentArea) return;

                // Handle remove item clicks
                if (e.target.closest('.remove-cart-item, .cart-remove, .remove-item')) {
                    e.preventDefault();
                    const removeBtn = e.target.closest('.remove-cart-item, .cart-remove, .remove-item');
                    const itemKey = removeBtn.getAttribute('data-key') || removeBtn.getAttribute('data-id');

                    if (itemKey) {
                        removeCartItem(itemKey);
                    }
                }

                // Handle quantity changes
                if (e.target.closest('.cart-qty-input, .quantity-input')) {
                    const qtyInput = e.target.closest('.cart-qty-input, .quantity-input');
                    const itemKey = qtyInput.getAttribute('data-key') || qtyInput.getAttribute('data-id');
                    const newQty = qtyInput.value;

                    if (itemKey && newQty) {
                        updateCartQuantity(itemKey, newQty);
                    }
                }
            });

            // Remove cart item function
            function removeCartItem(itemKey) {
                fetch('/cart/remove', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                    },
                    body: JSON.stringify({ key: itemKey })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error === false) {
                        // Reload cart content
                        loadCartContent();
                        // Update cart counter in header
                        updateCartCounter();
                        console.log('Item removed from cart');
                    }
                })
                .catch(error => {
                    console.log('Error removing item:', error);
                });
            }

            // Update cart quantity function
            function updateCartQuantity(itemKey, quantity) {
                fetch('/cart/update', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                    },
                    body: JSON.stringify({ key: itemKey, qty: quantity })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error === false) {
                        // Reload cart content
                        loadCartContent();
                        // Update cart counter in header
                        updateCartCounter();
                        console.log('Cart quantity updated');
                    }
                })
                .catch(error => {
                    console.log('Error updating quantity:', error);
                });
            }

            // Update cart counter in header
            function updateCartCounter() {
                const cartCounters = document.querySelectorAll('.header-item-counter, .cart-counter');

                fetch('/cart/count', {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    cartCounters.forEach(counter => {
                        counter.textContent = data.count || 0;
                    });
                })
                .catch(error => {
                    console.log('Error updating cart counter:', error);
                });
            }

            // Close button click
            closeBtn.addEventListener('click', closeCart);

            // Overlay click
            overlay.addEventListener('click', closeCart);

            // ESC key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeCart();
                }
            });

            console.log('NEW WORKING CART SIDEBAR CREATED AND READY!');
        });





    </script>

    <!-- Inline script to immediately fix doubled prices -->
    <script>
        // Immediately executing function to fix doubled prices
        (function() {
            // Function to fix doubled price display
            function fixDoubledPrices() {
                // Get all product cards
                const productCards = document.querySelectorAll('.product-inner');

                // Loop through each card
                productCards.forEach(function(card) {
                    // Get all price elements but exclude the main product-price containers
                    const priceElements = Array.from(card.querySelectorAll('.bb-product-price:not(.product-price), .price:not(.product-price), .price-amount:not(.product-price .price-amount), [class*="price"]:not(.product-price):not(.product-price-original):not(.product-price-sale)'));

                    // Only hide truly duplicate elements, not the main price containers
                    if (priceElements.length > 1) {
                        for (let i = 1; i < priceElements.length; i++) {
                            // Only hide if it's not a main price container
                            if (!priceElements[i].closest('.product-price') &&
                                !priceElements[i].classList.contains('product-price') &&
                                !priceElements[i].classList.contains('product-price-original') &&
                                !priceElements[i].classList.contains('product-price-sale')) {
                                priceElements[i].style.display = 'none';
                                priceElements[i].style.visibility = 'hidden';
                                priceElements[i].style.opacity = '0';
                                priceElements[i].style.position = 'absolute';
                                priceElements[i].style.left = '-9999px';
                            }
                        }
                    }
                });
            }

            // Run when DOM is loaded
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', fixDoubledPrices);
            } else {
                fixDoubledPrices();
            }

            // Run periodically but less frequently
            setInterval(fixDoubledPrices, 2000);
        })();
    </script>

    <style>
        /* Mobile Header Styles */
        @media (max-width: 768px) {
            body {
                padding-top: 0 !important;
            }

            /* Base header styles */
            .header-mobile {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                width: 100% !important;
                height: 60px !important;
                background: #f63; !important;
                z-index: 999999 !important;
                display: flex !important;
                align-items: center !important;
            }

            /* First content adjustment */
            #main-content {
                margin-top: 60px !important;
            }

            /* Hide desktop elements */
            .header-top,
            .header-middle,
            .header-bottom {
                display: none !important;
            }

            /* Homepage specific styles */
            body.home .header-mobile {
                padding: 10px 0 !important;
            }

            body.home .header-items-mobile--left {
                width: 50px !important;
                padding-left: 10px !important;
            }

            body.home .header-items-mobile--center {
                flex: 1 !important;
                padding-right: 10px !important;
            }

            /* Regular pages styles */
            body:not(.home) .header-mobile {
                padding: 10px !important;
            }

            body:not(.home) .header-items-mobile--left {
                width: 40px !important;
            }

            body:not(.home) .header-items-mobile--center {
                flex: 1 !important;
                padding: 0 10px !important;
            }

            body:not(.home) .header-items-mobile--right {
                width: 40px !important;
            }

            /* Common elements */
            .menu-mobile .menu-icon,
            .back-button a {
                width: 40px !important;
                height: 40px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            .menu-mobile svg,
            .back-button svg {
                width: 24px !important;
                height: 24px !important;
                fill: #ffffff !important;
            }

            .search-form--mobile {
                width: 100% !important;
            }

            .search-wrapper {
                display: flex !important;
                align-items: center !important;
                background: #ffffff !important;
                border-radius: 8px !important;
                overflow: hidden !important;
                height: 40px !important;
            }

            .form--quick-search input {
                flex: 1 !important;
                height: 40px !important;
                padding: 0 15px !important;
                border: none !important;
                background: transparent !important;
                font-size: 14px !important;
            }

            .form--quick-search input:focus {
                outline: none !important;
            }

            .btn-search {
                width: 40px !important;
                height: 40px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                background: transparent !important;
                border: none !important;
                padding: 0 !important;
            }

            .btn-search svg {
                width: 20px !important;
                height: 20px !important;
                fill: #ff6633 !important;
            }
        }
    </style>
</head>

<body <?php echo Theme::bodyAttributes(); ?>>
    <?php if(theme_option('preloader_enabled', 'yes') == 'yes'): ?>
        <?php echo Theme::partial('preloader'); ?>

    <?php endif; ?>

    <?php echo Theme::partial('svg-icons'); ?>

    <?php echo apply_filters(THEME_FRONT_BODY, null); ?>


    <header
        class="header header-js-handler"
        data-sticky="<?php echo e(theme_option('sticky_header_enabled', 'yes') == 'yes' ? 'true' : 'false'); ?>"
    >
        <div class="<?php echo \Illuminate\Support\Arr::toCssClasses([
            'header-top d-none d-lg-block',
            'header-content-sticky' =>
                theme_option('sticky_header_content_position', 'middle') == 'top',
        ]); ?>">
            <div class="container-xxxl">
                <div class="header-wrapper">
                    <div class="row align-items-center">
                        <div class="col-6">
                            <div class="header-info">
                                <?php echo Menu::renderMenuLocation('header-navigation', ['view' => 'menu-default']); ?>

                            </div>
                        </div>
                        <div class="col-6">
                            <div class="header-info header-info-right">
                                <ul>
                                    <?php if(is_plugin_active('language')): ?>
                                        <?php echo Theme::partial('language-switcher'); ?>

                                    <?php endif; ?>
                                    <?php if(is_plugin_active('ecommerce')): ?>
                                        <?php if(count($currencies) > 1): ?>
                                            <li>
                                                <a
                                                    class="language-dropdown-active"
                                                    href="#"
                                                >
                                                    <span><?php echo e(get_application_currency()->title); ?></span>
                                                    <span class="svg-icon">
                                                        <svg>
                                                            <use
                                                                href="#svg-icon-chevron-down"
                                                                xlink:href="#svg-icon-chevron-down"
                                                            ></use>
                                                        </svg>
                                                    </span>
                                                </a>
                                                <ul class="language-dropdown">
                                                    <?php $__currentLoopData = $currencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <?php if($currency->id !== get_application_currency_id()): ?>
                                                            <li>
                                                                <a
                                                                    href="<?php echo e(route('public.change-currency', $currency->title)); ?>">
                                                                    <span><?php echo e($currency->title); ?></span>
                                                                </a>
                                                            </li>
                                                        <?php endif; ?>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </ul>
                                            </li>
                                        <?php endif; ?>
                                        <?php if(auth('customer')->check()): ?>
                                            <li>
                                                <a
                                                    href="<?php echo e(route('customer.overview')); ?>"><?php echo e(auth('customer')->user()->name); ?></a>
                                                <span class="d-inline-block ms-1">(<a
                                                        class="color-primary"
                                                        href="<?php echo e(route('customer.logout')); ?>"
                                                    ><?php echo e(__('Logout')); ?></a>)</span>
                                            </li>
                                        <?php else: ?>
                                            <li><a href="<?php echo e(route('customer.login')); ?>"><?php echo e(__('Login')); ?></a></li>
                                            <li><a href="<?php echo e(route('customer.register')); ?>"><?php echo e(__('Register')); ?></a>
                                            </li>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="<?php echo \Illuminate\Support\Arr::toCssClasses([
            'header-middle',
            'header-content-sticky' =>
                theme_option('sticky_header_content_position', 'middle') == 'middle',
        ]); ?>">
            <div class="container-xxxl">
                <div class="header-wrapper">
                    <div class="header-items header__left">
                        <div class="logo">
                            <a href="<?php echo e(BaseHelper::getHomepageUrl()); ?>">
                                <?php echo Theme::getLogoImage(['style' => 'max-height: 40px']); ?>

                            </a>
                        </div>
                    </div>
                    <div class="header-items header__center">
                        <?php if(is_plugin_active('ecommerce')): ?>
                            <?php if (isset($component)) { $__componentOriginal8a03368ec6e49e00ad030dd0f1968073 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8a03368ec6e49e00ad030dd0f1968073 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'ccf469bf367e9d43e8287323c43b8fe7::fronts.ajax-search.index','data' => ['class' => 'form--quick-search']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('plugins-ecommerce::fronts.ajax-search'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'form--quick-search']); ?>
                                <?php if (isset($component)) { $__componentOriginald7d73f83e04d5f260717ce3bbffc01d3 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald7d73f83e04d5f260717ce3bbffc01d3 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'ccf469bf367e9d43e8287323c43b8fe7::fronts.ajax-search.input','data' => ['type' => 'text','class' => 'form-control input-search-product','placeholder' => ''.e(__('Search for anything')).'']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('plugins-ecommerce::fronts.ajax-search.input'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'text','class' => 'form-control input-search-product','placeholder' => ''.e(__('Search for anything')).'']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald7d73f83e04d5f260717ce3bbffc01d3)): ?>
<?php $attributes = $__attributesOriginald7d73f83e04d5f260717ce3bbffc01d3; ?>
<?php unset($__attributesOriginald7d73f83e04d5f260717ce3bbffc01d3); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald7d73f83e04d5f260717ce3bbffc01d3)): ?>
<?php $component = $__componentOriginald7d73f83e04d5f260717ce3bbffc01d3; ?>
<?php unset($__componentOriginald7d73f83e04d5f260717ce3bbffc01d3); ?>
<?php endif; ?>
                                <button class="btn" type="submit" aria-label="Submit">
                                    <span class="svg-icon">
                                        <svg>
                                            <use href="#svg-icon-search" xlink:href="#svg-icon-search"></use>
                                        </svg>
                                    </span>
                                </button>
                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8a03368ec6e49e00ad030dd0f1968073)): ?>
<?php $attributes = $__attributesOriginal8a03368ec6e49e00ad030dd0f1968073; ?>
<?php unset($__attributesOriginal8a03368ec6e49e00ad030dd0f1968073); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8a03368ec6e49e00ad030dd0f1968073)): ?>
<?php $component = $__componentOriginal8a03368ec6e49e00ad030dd0f1968073; ?>
<?php unset($__componentOriginal8a03368ec6e49e00ad030dd0f1968073); ?>
<?php endif; ?>
                        <?php endif; ?>
                    </div>
                    <div class="header-items header__right">
                        <?php if(is_plugin_active('ecommerce')): ?>
                            <?php if(EcommerceHelper::isWishlistEnabled()): ?>
                                <div class="header__extra header-wishlist">
                                    <a class="btn-wishlist" href="<?php echo e(route('public.wishlist')); ?>">
                                        <span class="svg-icon">
                                            <svg>
                                                <use href="#svg-icon-wishlist" xlink:href="#svg-icon-wishlist"></use>
                                            </svg>
                                        </span>
                                        <span class="header-item-counter">
                                            <?php echo e(auth('customer')->check() ? auth('customer')->user()->wishlist()->count() : Cart::instance('wishlist')->count()); ?>

                                        </span>
                                    </a>
                                </div>
                            <?php endif; ?>
                            <?php if(EcommerceHelper::isCartEnabled()): ?>
                                <div class="header__extra cart--mini">
                                    <a class="btn-shopping-cart toggle--sidebar" href="#cart-mobile" data-bb-toggle="cart-sidebar">
                                        <span class="svg-icon">
                                            <svg>
                                                <use href="#svg-icon-cart" xlink:href="#svg-icon-cart"></use>
                                            </svg>
                                        </span>
                                        <span class="header-item-counter"><?php echo e(Cart::instance('cart')->count()); ?></span>
                                    </a>
                                    <div class="cart__content panel--sidebar panel--sidebar__right" id="cart-mobile">
                                        <div class="panel__content">
                                            <div class="panel__header">
                                                <h4><?php echo e(__('Shopping Cart')); ?></h4>
                                                <button class="btn btn-close-sidebar" data-bb-toggle="close-sidebar">
                                                    <span class="svg-icon">
                                                        <svg>
                                                            <use href="#svg-icon-times" xlink:href="#svg-icon-times"></use>
                                                        </svg>
                                                    </span>
                                                </button>
                                            </div>
                                            <div class="mini-cart-content">
                                                <div class="widget-shopping-cart-content">
                                                    <?php echo Theme::partial('cart-mini.list'); ?>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="<?php echo \Illuminate\Support\Arr::toCssClasses([
            'header-bottom',
            'header-content-sticky' =>
                theme_option('sticky_header_content_position', 'middle') == 'bottom',
        ]); ?>">
            <div class="header-wrapper">
                <nav class="navigation">
                    <div class="container-xxxl">
                        <div class="navigation__left">
                            <?php if(is_plugin_active('ecommerce') && theme_option('enabled_product_categories_on_header', 'yes') == 'yes'): ?>
                                <div class="menu--product-categories">
                                    <div class="menu__toggle">
                                        <span class="svg-icon">
                                            <svg>
                                                <use
                                                    href="#svg-icon-list"
                                                    xlink:href="#svg-icon-list"
                                                ></use>
                                            </svg>
                                        </span>
                                        <span class="menu__toggle-title"><?php echo e(__('Categories')); ?></span>
                                    </div>
                                    <div
                                        class="menu__content"
                                        data-bb-toggle="init-categories-dropdown"
                                        data-bb-target=".menu--dropdown"
                                        data-url="<?php echo e(route('public.ajax.categories-dropdown')); ?>"
                                    >
                                        <ul class="menu--dropdown"></ul>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['navigation__center', 'ps-0' => theme_option('enabled_product_categories_on_header', 'yes') != 'yes']); ?>">
                            <?php echo Menu::renderMenuLocation('main-menu', [
                                'view' => 'menu',
                                'options' => ['class' => 'menu'],
                            ]); ?>

                        </div>
                        <div class="navigation__right">
                            <?php if(is_plugin_active('ecommerce') && EcommerceHelper::isEnabledCustomerRecentlyViewedProducts()): ?>
                                <div
                                    class="header-recently-viewed"
                                    data-url="<?php echo e(route('public.ajax.recently-viewed-products')); ?>"
                                    role="button"
                                >
                                    <h3 class="recently-title">
                                        <span class="svg-icon recent-icon">
                                            <svg>
                                                <use
                                                    href="#svg-icon-refresh"
                                                    xlink:href="#svg-icon-refresh"
                                                ></use>
                                            </svg>
                                        </span>
                                        <?php echo e(__('Recently Viewed')); ?>

                                    </h3>
                                    <div class="recently-viewed-inner container-xxxl">
                                        <div class="recently-viewed-content">
                                            <div class="loading--wrapper">
                                                <div class="loading"></div>
                                            </div>
                                            <div class="recently-viewed-products"></div>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </nav>
            </div>
        </div>
    </header>

    <!-- Cart Sidebar Overlay -->
    <div class="sidebar-overlay" id="cart-sidebar-overlay"></div>

    <!-- Mobile Header - Recreated -->
    <style>
        /* Force mobile header styles */
        @media (max-width: 992px) {
            body {
                padding-top: 30px !important;
                margin-top: 0 !important;
            }
            html {
                margin-top: 0 !important;
                padding-top: 0 !important;
            }
            /* Aggressive gap removal - more targeted */
            body > *:not(.header-mobile-new) {
                margin-top: 0 !important;
                padding-top: 0 !important;
            }
            .main-content, .page-content, .container, .content-area,
            .page-wrapper, .site-content, .content-wrapper,
            main, section, article, .row, .col, .breadcrumb,
            .page-header, .content, .site-main, .primary,
            .entry-content, .post, .product, .shop, .archive {
                margin-top: 0 !important;
                padding-top: 0 !important;
            }
            /* Target specific theme elements */
            .page-title, .page-breadcrumb, .breadcrumb-wrapper,
            .hero-section, .banner, .slider, .carousel {
                margin-top: 0 !important;
                padding-top: 0 !important;
            }
            .header-mobile-new {
                display: flex !important;
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                right: 0 !important;
                background: #ffffff !important;
                height: 60px !important;
                z-index: 1001 !important;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                border-bottom: none !important;
            }
            /* Force content to start immediately after header */
            .header-mobile-new + *,
            body > *:not(.header-mobile-new):first-of-type {
                margin-top: -20px !important;
            }
            /* Additional targeting for common content wrappers */
            .page-wrapper, .site-content, .main-content,
            .content-area, .primary, .site-main {
                margin-top: -20px !important;
            }
            /* Target all possible content containers */
            body > div:not(.header-mobile-new),
            body > main:not(.header-mobile-new),
            body > section:not(.header-mobile-new) {
                margin-top: -20px !important;
            }
            /* Ultra aggressive targeting */
            body > *:not(.header-mobile-new) {
                margin-top: -20px !important;
            }



            /* Force sticky add-to-cart to be visible in mobile view - preserve original design */
            #sticky-add-to-cart,
            #sticky-add-to-cart .sticky-atc-wrap,
            .sticky-add-to-cart,
            .sticky-atc-wrap,
            .sticky-bar,
            .product-sticky-bar,
            .mobile-sticky-add-to-cart {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                position: fixed !important;
                bottom: 60px !important;
                left: 0 !important;
                right: 0 !important;
                z-index: 9999 !important;
                width: 100% !important;
                transform: translate3d(0, 0, 0) !important;
            }

            /* Ensure sticky add-to-cart content and buttons are visible */
            #sticky-add-to-cart *,
            #sticky-add-to-cart .sticky-atc-wrap *,
            .sticky-atc-wrap * {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            /* Force sticky buttons to be visible */
            #sticky-add-to-cart .sticky-atc-btn,
            #sticky-add-to-cart .btn,
            .sticky-atc-wrap .btn {
                display: flex !important;
                visibility: visible !important;
                opacity: 1 !important;
            }

            /* Fix mobile menu scroll lock issue */
            body.menu-open,
            body.mobile-menu-open,
            body.nav-open,
            body.sidebar-open {
                overflow: auto !important;
                position: static !important;
                height: auto !important;
            }

            /* Ensure mobile menu doesn't prevent scrolling */
            .mobile-menu-overlay,
            .menu-overlay,
            .sidebar-overlay {
                position: fixed !important;
                overflow-y: auto !important;
                -webkit-overflow-scrolling: touch !important;
            }

            /* Allow scrolling in mobile menu */
            .mobile-menu,
            .sidebar-menu,
            .nav-menu {
                overflow-y: auto !important;
                -webkit-overflow-scrolling: touch !important;
                max-height: 100vh !important;
            }

            /* Fix mobile menu sidebar positioning - make it full height (exclude cart) */
            .panel--sidebar:not(#cart-mobile),
            #menu-mobile,
            .menu-mobile-wrapper {
                top: 0 !important;
                bottom: 0 !important;
                height: 100vh !important;
                min-height: 100vh !important;
                max-height: 100vh !important;
                z-index: 99999 !important;
                position: fixed !important;
            }

            /* Ensure menu content accounts for header space and fills full height (exclude cart) */
            .panel--sidebar:not(#cart-mobile) .panel__content {
                padding-top: 80px !important;
                padding-bottom: 20px !important;
                height: 100vh !important;
                min-height: 100vh !important;
                overflow-y: auto !important;
                box-sizing: border-box !important;
            }

            /* Cart sidebar specific styles - make it work like mobile menu */
            #cart-mobile {
                position: fixed !important;
                top: 0 !important;
                right: 0 !important;
                width: 82% !important;
                max-width: 400px !important;
                height: 100vh !important;
                overflow-y: auto !important;
                z-index: 99999 !important;
                background-color: #fff !important;
                transform: translateX(101%) !important;
                transition: all 0.5s cubic-bezier(0.7, 0, 0.3, 1) 0s !important;
            }

            #cart-mobile.active {
                transform: translateX(0) !important;
            }

            #cart-mobile .panel__content {
                padding-top: 20px !important;
                padding-bottom: 20px !important;
                height: 100% !important;
                overflow-y: auto !important;
                box-sizing: border-box !important;
            }
            .mobile-header-container {
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                width: 100% !important;
                height: 100% !important;
                padding: 0 16px !important;
            }
            .mobile-header-left {
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                width: 40px !important;
                height: 100% !important;
            }
            .mobile-header-center {
                flex: 1 !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                height: 100% !important;
                margin-left: 12px !important;
            }
            .mobile-header-right {
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                width: 40px !important;
                height: 100% !important;
            }
            .mobile-header-center {
                margin-right: 12px !important;
            }
            .mobile-search-form {
                width: 100% !important;
                height: 40px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }
            .form--quick-search {
                width: 100% !important;
                height: 100% !important;
                display: flex !important;
                align-items: center !important;
            }
            /* Homepage specific styles */
            body.home .mobile-search-form {
                height: 50px !important;
            }
            body.home .search-input-wrapper {
                height: 50px !important;
                border-radius: 25px !important;
            }
            body.home .search-input-wrapper .form-control {
                line-height: 50px !important;
                height: 50px !important;
                font-size: 16px !important;
                padding: 0 70px 0 20px !important;
            }
            body.home .search-submit-btn {
                width: 60px !important;
                height: 100% !important;
                border-radius: 0 25px 25px 0 !important;
                right: 0 !important;
                top: 0 !important;
                bottom: 0 !important;
                box-shadow: 0 3px 12px rgba(255, 102, 51, 0.4) !important;
            }
            body.home .search-submit-btn:hover {
                box-shadow: 0 5px 16px rgba(255, 102, 51, 0.5) !important;
            }
            body.home .search-submit-btn .svg-icon {
                font-size: 18px !important;
            }
            body.home .mobile-menu-trigger .menu-icon-wrapper {
                width: 40px !important;
                height: 40px !important;
            }
            body.home .mobile-menu-trigger .menu-icon-wrapper .svg-icon {
                font-size: 24px !important;
            }
            .search-input-wrapper {
                display: flex !important;
                position: relative !important;
                width: 100% !important;
                height: 100% !important;
                background: #ffffff !important;
                border-radius: 20px !important;
                border: 2px solid #000000 !important;
            }
            .search-input-wrapper .form-control {
                border: none !important;
                background: transparent !important;
                padding: 0 60px 0 16px !important;
                font-size: 14px !important;
                line-height: 40px !important;
                height: 40px !important;
                width: 100% !important;
                outline: none !important;
                color: #333 !important;
            }
            .search-submit-btn {
                position: absolute !important;
                right: 0 !important;
                top: 0 !important;
                bottom: 0 !important;
                transform: none !important;
                background: linear-gradient(135deg, #ff6633 0%, #e55a2b 100%) !important;
                border: none !important;
                width: 50px !important;
                height: 100% !important;
                border-radius: 0 20px 20px 0 !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                box-shadow: 0 2px 8px rgba(255, 102, 51, 0.3) !important;
                transition: all 0.2s ease !important;
                cursor: pointer !important;
            }
            .search-submit-btn:hover {
                background: linear-gradient(135deg, #e55a2b 0%, #d14d1f 100%) !important;
                box-shadow: 0 4px 12px rgba(255, 102, 51, 0.4) !important;
                transform: none !important;
            }
            .search-submit-btn:active {
                transform: none !important;
                box-shadow: 0 1px 4px rgba(255, 102, 51, 0.3) !important;
                background: linear-gradient(135deg, #d14d1f 0%, #b8421a 100%) !important;
            }
            .search-submit-btn .svg-icon {
                font-size: 16px !important;
                color: #fff !important;
                filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)) !important;
            }
            .mobile-back-button .back-link,
            .mobile-menu-trigger .menu-icon-wrapper {
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                width: 32px !important;
                height: 32px !important;
                border-radius: 4px !important;
                margin: auto !important;
            }
            .mobile-back-button .back-link .svg-icon,
            .mobile-menu-trigger .menu-icon-wrapper .svg-icon {
                font-size: 18px !important;
                color: #333 !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }
        }
        @media (min-width: 993px) {
            body { padding-top: 0 !important; }
            .header-mobile-new { display: none !important; }

            /* Force desktop header to show properly from 993px onwards */
            .header {
                display: block !important;
            }
            .header .header-top {
                display: block !important;
            }
            .header .header-middle {
                display: block !important;
                border-bottom: 1px solid #eee !important;
            }
            .header .header-middle .header-wrapper {
                display: flex !important;
                align-items: center !important;
                padding: 15px 0 !important;
            }
            .header .header-middle .header-wrapper .header__left {
                display: block !important;
            }
            .header .header-middle .header-wrapper .header__center {
                display: block !important;
            }
            .header .header-middle .header-wrapper .header__right {
                display: block !important;
            }
            .header .header-middle .header-wrapper .header__right .header-support {
                display: block !important;
            }
            .header .header-middle .header-wrapper .header__right .cart--mini .header__extra {
                display: block !important;
            }
            .header .header-bottom {
                display: block !important;
            }

            /* Hide all mobile sticky elements from 993px onwards */
            .sticky-bottom,
            .mobile-sticky-bottom,
            .bottom-sticky,
            .sticky-mobile-bottom,
            .mobile-bottom-bar,
            .bottom-navigation,
            .mobile-navigation-bottom,
            .sticky-navigation-bottom,
            .footer-mobile,
            .menu--footer,
            #sticky-add-to-cart {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                position: absolute !important;
                left: -9999px !important;
                top: -9999px !important;
                width: 0 !important;
                height: 0 !important;
                overflow: hidden !important;
                z-index: -1 !important;
            }

            /* GLOBAL MOBILE HIDE - Location Selector */
            @media (max-width: 992px) {
                .location-popup-overlay,
                .location-popup,
                #location-popup-overlay,
                #location-popup,
                .shipping-location-selector,
                #shipping-location-btn,
                .shipping-info,
                .location-step,
                .location-popup-header,
                .location-popup-content,
                .location-popup-title,
                .location-popup-close,
                .location-list,
                .location-item,
                .location-loading,
                .location-back-btn,
                .location-step-title {
                    display: none !important;
                    visibility: hidden !important;
                    opacity: 0 !important;
                    pointer-events: none !important;
                    position: absolute !important;
                    left: -9999px !important;
                    top: -9999px !important;
                    width: 0 !important;
                    height: 0 !important;
                    overflow: hidden !important;
                    z-index: -1 !important;
                }
            }

        }
    </style>

    <script>
        // AGGRESSIVE MOBILE LOCATION SELECTOR HIDE
        function hideLocationSelectorOnMobile() {
            if (window.innerWidth <= 992) {
                // List of all possible location selector elements
                const selectors = [
                    '.location-popup-overlay',
                    '.location-popup',
                    '#location-popup-overlay',
                    '#location-popup',
                    '.shipping-location-selector',
                    '#shipping-location-btn',
                    '.shipping-info',
                    '.location-step',
                    '.location-popup-header',
                    '.location-popup-content',
                    '.location-popup-title',
                    '.location-popup-close',
                    '.location-list',
                    '.location-item',
                    '.location-loading',
                    '.location-back-btn',
                    '.location-step-title'
                ];

                selectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(element => {
                        element.style.display = 'none';
                        element.style.visibility = 'hidden';
                        element.style.opacity = '0';
                        element.style.pointerEvents = 'none';
                        element.style.position = 'absolute';
                        element.style.left = '-9999px';
                        element.style.top = '-9999px';
                        element.style.width = '0';
                        element.style.height = '0';
                        element.style.overflow = 'hidden';
                        element.style.zIndex = '-1';
                        element.remove(); // Completely remove from DOM
                    });
                });

                console.log('Location selector elements forcefully hidden/removed on mobile');
            }
        }

        // Run immediately
        hideLocationSelectorOnMobile();

        // Run on DOM ready
        document.addEventListener('DOMContentLoaded', hideLocationSelectorOnMobile);

        // Run on window resize
        window.addEventListener('resize', hideLocationSelectorOnMobile);

        // Run periodically to catch dynamically added elements
        setInterval(hideLocationSelectorOnMobile, 1000);

        // Force existing sticky add-to-cart to be visible on mobile - preserve original design
        document.addEventListener('DOMContentLoaded', function() {
            if (window.innerWidth <= 992) {
                // Find all possible sticky bar elements
                const stickySelectors = [
                    '#sticky-add-to-cart',
                    '.sticky-atc-wrap',
                    '.sticky-add-to-cart',
                    '.sticky-bar',
                    '.product-sticky-bar'
                ];

                stickySelectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(element => {
                        if (element) {
                            // Force visibility without changing design
                            element.style.display = 'block';
                            element.style.visibility = 'visible';
                            element.style.opacity = '1';
                            element.style.position = 'fixed';
                            element.style.bottom = '60px';
                            element.style.left = '0';
                            element.style.right = '0';
                            element.style.zIndex = '9999';
                            element.style.transform = 'translate3d(0, 0, 0)';

                            // Also force visibility of child elements
                            const children = element.querySelectorAll('*');
                            children.forEach(child => {
                                child.style.display = '';
                                child.style.visibility = 'visible';
                                child.style.opacity = '1';
                            });
                        }
                    });
                });
            }
        });

        // Use homepage menu approach - prevent scroll lock by ensuring body stays scrollable
        document.addEventListener('click', function(e) {
            if (e.target.closest('.toggle--sidebar') ||
                e.target.closest('.mobile-menu-trigger') ||
                e.target.closest('.menu-icon-wrapper')) {

                // Use homepage approach - don't lock scroll
                setTimeout(function() {
                    document.body.style.overflow = 'auto';
                    document.body.style.position = 'static';
                    document.body.style.height = 'auto';
                }, 10);
            }
        });
    </script>
    <div
        class="header-mobile-new header-js-handler"
        data-sticky="<?php echo e(theme_option('sticky_header_mobile_enabled', 'yes') == 'yes' ? 'true' : 'false'); ?>"
    >
        <div class="mobile-header-container">
            <div class="mobile-header-left">
                <?php if(!request()->is('/')): ?>
                    <div class="mobile-back-button">
                        <a href="javascript:history.back()" class="back-link">
                            <span class="svg-icon">
                                <svg>
                                    <use href="#svg-icon-arrow-left" xlink:href="#svg-icon-arrow-left"></use>
                                </svg>
                            </span>
                        </a>
                    </div>
                <?php else: ?>
                    <div class="mobile-menu-trigger">
                        <div class="menu-icon-wrapper toggle--sidebar" href="#menu-mobile">
                            <span class="svg-icon">
                                <svg>
                                    <use href="#svg-icon-list" xlink:href="#svg-icon-list"></use>
                                </svg>
                            </span>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <div class="mobile-header-center">
                <div class="mobile-search-form">
                    <form class="form--quick-search" action="<?php echo e(route('public.products')); ?>" method="GET">
                        <div class="search-input-wrapper">
                            <input
                                class="form-control input-search-product"
                                name="q"
                                type="text"
                                placeholder="<?php echo e(__('Search products...')); ?>"
                                autocomplete="off"
                            >
                            <button class="search-submit-btn" type="submit">
                                <span class="svg-icon">
                                    <svg>
                                        <use href="#svg-icon-search" xlink:href="#svg-icon-search"></use>
                                    </svg>
                                </span>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="mobile-header-right">
                <!-- Right menu button removed to prevent duplicate -->
            </div>
        </div>
    </div>

    <!-- ULTRA AGGRESSIVE JavaScript to forcefully remove header border -->
    <script>
        (function() {
            function removeHeaderBorder() {
                // Find all possible header-middle elements with multiple selectors
                var selectors = [
                    '.header-middle',
                    '.header .header-middle',
                    'header .header-middle',
                    'body .header-middle',
                    'html .header-middle',
                    '.header-middle.header-content-sticky',
                    '.header .header-middle.header-content-sticky',
                    'header.header .header-middle',
                    'div.header-middle',
                    'section.header-middle',
                    '[class*="header-middle"]'
                ];

                selectors.forEach(function(selector) {
                    try {
                        var elements = document.querySelectorAll(selector);
                        for (var i = 0; i < elements.length; i++) {
                            var element = elements[i];
                            // Force remove border using multiple methods
                            element.style.borderBottom = 'none';
                            element.style.borderBottomWidth = '0';
                            element.style.borderBottomStyle = 'none';
                            element.style.borderBottomColor = 'transparent';
                            element.style.border = 'none';
                            element.style.boxShadow = 'none';
                            element.style.outline = 'none';
                            element.style.backgroundImage = 'none';

                            // Use setProperty with important flag
                            element.style.setProperty('border-bottom', 'none', 'important');
                            element.style.setProperty('border-bottom-width', '0', 'important');
                            element.style.setProperty('border-bottom-style', 'none', 'important');
                            element.style.setProperty('border-bottom-color', 'transparent', 'important');
                            element.style.setProperty('border', 'none', 'important');
                            element.style.setProperty('box-shadow', 'none', 'important');
                            element.style.setProperty('outline', 'none', 'important');
                            element.style.setProperty('background-image', 'none', 'important');

                            // Add a class to mark as processed
                            element.classList.add('border-removed-by-script');

                            // Set a custom attribute
                            element.setAttribute('data-border-removed', 'true');

                            // Also check child elements that might have borders
                            var children = element.querySelectorAll('*');
                            for (var j = 0; j < children.length; j++) {
                                var child = children[j];
                                child.style.setProperty('border-bottom', 'none', 'important');
                                child.style.setProperty('box-shadow', 'none', 'important');
                            }
                        }
                    } catch (e) {
                        // Ignore selector errors
                    }
                });

                // Also inject additional CSS dynamically
                var existingStyle = document.getElementById('remove-header-border-style');
                if (!existingStyle) {
                    var style = document.createElement('style');
                    style.id = 'remove-header-border-style';
                    style.innerHTML = \`
                        .header-middle,
                        .header .header-middle,
                        header .header-middle,
                        body .header-middle,
                        html .header-middle,
                        .header-middle.header-content-sticky,
                        .header .header-middle.header-content-sticky,
                        header.header .header-middle,
                        div.header-middle,
                        section.header-middle,
                        [class*="header-middle"],
                        .header-middle *,
                        .header-middle::before,
                        .header-middle::after,
                        .header-middle .header-wrapper,
                        .header-middle .header-wrapper::before,
                        .header-middle .header-wrapper::after {
                            border-bottom: none !important;
                            border-bottom-width: 0 !important;
                            border-bottom-style: none !important;
                            border-bottom-color: transparent !important;
                            border: none !important;
                            box-shadow: none !important;
                            outline: none !important;
                            background-image: none !important;
                        }

                        /* Remove any potential separators */
                        .header-middle + *,
                        .header-middle ~ .header-bottom,
                        .header-bottom::before,
                        .header-bottom::after {
                            border-top: none !important;
                            border-top-width: 0 !important;
                            border-top-style: none !important;
                            border-top-color: transparent !important;
                            box-shadow: none !important;
                            outline: none !important;
                        }
                    \`;
                    document.head.appendChild(style);
                }
            }

            // Run immediately
            removeHeaderBorder();

            // Run when DOM is loaded
            document.addEventListener('DOMContentLoaded', removeHeaderBorder);

            // Run when page is fully loaded
            window.addEventListener('load', removeHeaderBorder);

            // Run with multiple timeouts
            setTimeout(removeHeaderBorder, 50);
            setTimeout(removeHeaderBorder, 100);
            setTimeout(removeHeaderBorder, 200);
            setTimeout(removeHeaderBorder, 500);
            setTimeout(removeHeaderBorder, 1000);
            setTimeout(removeHeaderBorder, 2000);

            // Run periodically to ensure border stays removed
            setInterval(removeHeaderBorder, 1000);

            // Use MutationObserver to watch for DOM changes
            if (window.MutationObserver) {
                var observer = new MutationObserver(function(mutations) {
                    var shouldRun = false;
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList' || mutation.type === 'attributes') {
                            shouldRun = true;
                        }
                    });
                    if (shouldRun) {
                        setTimeout(removeHeaderBorder, 10);
                    }
                });

                observer.observe(document.body, {
                    childList: true,
                    subtree: true,
                    attributes: true,
                    attributeFilter: ['class', 'style']
                });
            }

            // Override any potential CSS reloading
            var originalCreateElement = document.createElement;
            document.createElement = function(tagName) {
                var element = originalCreateElement.call(document, tagName);
                if (tagName.toLowerCase() === 'style' || tagName.toLowerCase() === 'link') {
                    setTimeout(removeHeaderBorder, 100);
                }
                return element;
            };
        })();
    </script>
</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\uu\platform\themes/farmart/partials/header.blade.php ENDPATH**/ ?>