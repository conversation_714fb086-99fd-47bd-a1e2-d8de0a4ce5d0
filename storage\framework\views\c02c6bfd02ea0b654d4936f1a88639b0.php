<!DOCTYPE html>
<html <?php echo Theme::htmlAttributes(); ?>>
<head>
    <meta charset="UTF-8">
    <meta
        name="viewport"
        content="width=device-width, initial-scale=1"
    >
    <meta
        name="csrf-token"
        content="<?php echo e(csrf_token()); ?>"
    >
    <title> <?php echo $__env->yieldContent('title', __('Checkout')); ?> </title>

    <?php if(theme_option('favicon')): ?>
        <link
            href="<?php echo e(RvMedia::getImageUrl(theme_option('favicon'))); ?>"
            rel="shortcut icon"
        >
    <?php endif; ?>

    <?php echo Theme::typography()->renderCssVariables(); ?>


    <style>
        :root {
            --primary-color: <?php echo e($primaryColor = theme_option('primary_color', '#58b3f0')); ?>;
            --primary-color-rgb: <?php echo e(implode(',', BaseHelper::hexToRgb($primaryColor))); ?>;
        }

        /* Delete button styling */
        .remove-cart-item {
            transition: all 0.3s ease;
            opacity: 0.9;
            color: #ff0000 !important; /* Red color */
        }

        .remove-cart-item:hover {
            opacity: 1;
            color: #cc0000 !important; /* Darker red on hover */
        }

        .remove-cart-item.loading {
            pointer-events: none;
            opacity: 0.5;
        }

        /* Make sure the delete button is visible */
        .cart-item {
            position: relative;
        }
    </style>

    <?php echo Html::style('vendor/core/core/base/libraries/font-awesome/css/fontawesome.min.css'); ?>

    <?php echo Html::style('vendor/core/core/base/libraries/ckeditor/content-styles.css?v=3.8.0'); ?>

    <?php echo Html::style('vendor/core/plugins/ecommerce/css/front-theme.css?v=3.8.0'); ?>


    <?php if(BaseHelper::isRtlEnabled()): ?>
        <?php echo Html::style('vendor/core/plugins/ecommerce/css/front-theme-rtl.css?v=3.8.0'); ?>

    <?php endif; ?>

    <?php echo Html::style('vendor/core/core/base/libraries/toastr/toastr.min.css'); ?>


    <?php echo Html::script('vendor/core/plugins/ecommerce/js/checkout.js?v=3.8.0'); ?>


    <?php if(EcommerceHelper::loadCountriesStatesCitiesFromPluginLocation()): ?>
        <link
            href="<?php echo e(asset('vendor/core/core/base/libraries/select2/css/select2.min.css')); ?>"
            rel="stylesheet"
        >
        <script src="<?php echo e(asset('vendor/core/core/base/libraries/select2/js/select2.min.js')); ?>"></script>
        <script src="<?php echo e(asset('vendor/core/plugins/location/js/location.js?v=3.8.0')); ?>"></script>
    <?php endif; ?>

    <?php echo apply_filters('ecommerce_checkout_header', null); ?>


    <?php echo $__env->yieldPushContent('header'); ?>
</head>

<?php
    Theme::addBodyAttributes([
        'class' => 'checkout-page',
    ]);
?>

<body<?php echo Theme::bodyAttributes(); ?>>
    <?php echo apply_filters('ecommerce_checkout_body', null); ?>

    <div class="container my-0 my-md-3 my-lg-5 checkout-content-wrap">
        <?php echo $__env->yieldContent('content'); ?>
    </div>

    <?php echo $__env->yieldPushContent('footer'); ?>

    <?php echo Html::script('vendor/core/plugins/ecommerce/js/utilities.js?v=3.8.0'); ?>

    <?php echo Html::script('vendor/core/core/base/libraries/toastr/toastr.min.js'); ?>


    <script type="text/javascript">
        window.messages = {
            error_header: '<?php echo e(__('Error')); ?>',
            success_header: '<?php echo e(__('Success')); ?>',
        }

        // Handle remove cart item in checkout page
        $(document).on('click', '.remove-cart-item', function (event) {
            event.preventDefault();
            let _self = $(this);

            $.ajax({
                url: _self.data('url'),
                method: 'GET',
                beforeSend: () => {
                    _self.addClass('loading');
                },
                success: (res) => {
                    if (res.error) {
                        alert(res.message);
                        return false;
                    }

                    // Reload the page after successful removal
                    window.location.reload();
                },
                error: (res) => {
                    alert('An error occurred. Please try again later.');
                },
                complete: () => {
                    _self.removeClass('loading');
                },
            });
        });
    </script>

    <?php if(session()->has('success_msg') || session()->has('error_msg') || isset($errors)): ?>
        <script type="text/javascript">
            $(document).ready(function() {
                <?php if(session()->has('success_msg') && session('success_msg')): ?>
                    MainCheckout.showNotice('success', '<?php echo e(session('success_msg')); ?>');
                <?php endif; ?>
                <?php if(session()->has('error_msg')): ?>
                    MainCheckout.showNotice('error', '<?php echo e(session('error_msg')); ?>');
                <?php endif; ?>
                <?php if(isset($errors) && $errors->count()): ?>
                    MainCheckout.showNotice('error', '<?php echo e($errors->first()); ?>');
                <?php endif; ?>
            });
        </script>
    <?php endif; ?>

    <?php echo apply_filters('ecommerce_checkout_footer', null); ?>


</body>
</html>
<?php /**PATH C:\Users\<USER>\Desktop\uu\platform/plugins/ecommerce/resources/views/orders/master.blade.php ENDPATH**/ ?>