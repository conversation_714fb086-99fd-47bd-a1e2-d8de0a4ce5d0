@php
    $shipping = array_filter($shipping ?? []);

    // Get location data from session to determine auto-selection
    $sessionData = session('checkout_address_data', []);
    $token = session('tracked_start_checkout');
    if ($token) {
        $sessionKey = md5('checkout_address_information_' . $token);
        $sessionLocationData = session($sessionKey, []);
        if (!empty($sessionLocationData)) {
            $sessionData = array_merge($sessionData, $sessionLocationData);
        }
    }

    $selectedState = $sessionData['state'] ?? null;
    $selectedCity = $sessionData['city'] ?? null;

    // Determine if location is in Dhaka - enhanced detection
    $isDhakaLocation = false;
    if ($selectedState) {
        // Check if state name contains "Dhaka" (case insensitive)
        $isDhakaLocation = stripos($selectedState, 'dhaka') !== false;

        // Also check for common variations
        if (!$isDhakaLocation) {
            $dhakaVariations = ['dhaka', 'ঢাকা', 'dhaka division', 'dhaka district'];
            foreach ($dhakaVariations as $variation) {
                if (stripos($selectedState, $variation) !== false) {
                    $isDhakaLocation = true;
                    break;
                }
            }
        }
    }

    // Additional check using city name if state check is inconclusive
    if (!$isDhakaLocation && $selectedCity) {
        $dhakaCities = ['dhaka', 'ঢাকা', 'dhanmondi', 'gulshan', 'banani', 'uttara', 'mirpur', 'wari', 'old dhaka'];
        foreach ($dhakaCities as $dhakaCity) {
            if (stripos($selectedCity, $dhakaCity) !== false) {
                $isDhakaLocation = true;
                break;
            }
        }
    }

    // Auto-select shipping method based on location
    $autoSelectedMethod = null;
    $autoSelectedOption = null;

    if (!empty($shipping) && ($selectedState || $selectedCity)) {
        foreach ($shipping as $shippingKey => $shippingItems) {
            foreach ($shippingItems as $shippingOption => $shippingItem) {
                $shippingName = strtolower($shippingItem['name'] ?? '');

                // Auto-select based on location
                if ($isDhakaLocation && (strpos($shippingName, 'inside dhaka') !== false || strpos($shippingName, 'dhaka city') !== false)) {
                    $autoSelectedMethod = $shippingKey;
                    $autoSelectedOption = $shippingOption;
                    break 2;
                } elseif (!$isDhakaLocation && (strpos($shippingName, 'outside dhaka') !== false || strpos($shippingName, 'outside') !== false)) {
                    $autoSelectedMethod = $shippingKey;
                    $autoSelectedOption = $shippingOption;
                    break 2;
                }
            }
        }
    }

    // Use auto-selected method if available, otherwise use default
    $finalDefaultMethod = $autoSelectedMethod ?? old('shipping_method', $defaultShippingMethod);
    $finalDefaultOption = $autoSelectedOption ?? old('shipping_option', $defaultShippingOption);
@endphp

@if (! empty($shipping))
    <div class="modern-shipping-methods-container">
        <input
            name="shipping_option"
            type="hidden"
            value="{{ BaseHelper::stringify($finalDefaultOption) }}"
        >

        @if ($selectedState || $selectedCity)
            <div class="location-info-banner">
                <div class="location-info-content">
                    <i class="fas fa-map-marker-alt"></i>
                    <span class="location-text">
                        {{ __('Delivering to') }}:
                        <strong>{{ $selectedCity ? $selectedCity : '' }}{{ $selectedCity && $selectedState ? ', ' : '' }}{{ $selectedState ? $selectedState : '' }}</strong>
                        @if ($isDhakaLocation)
                            <small class="location-type">({{ __('Inside Dhaka') }})</small>
                        @else
                            <small class="location-type">({{ __('Outside Dhaka') }})</small>
                        @endif
                    </span>
                    @if ($autoSelectedMethod)
                        <span class="auto-selected-badge">{{ __('Auto-selected') }}</span>
                    @endif
                </div>
            </div>
        @endif

        <div class="shipping-methods-grid">
            @foreach ($shipping as $shippingKey => $shippingItems)
                @foreach ($shippingItems as $shippingOption => $shippingItem)
                    @include(
                        'plugins/ecommerce::orders.partials.shipping-option',
                        [
                            'shippingItem' => $shippingItem,
                            'attributes' => [
                                'id' => "shipping-method-$shippingKey-$shippingOption",
                                'name' => 'shipping_method',
                                'class' => 'modern-shipping-radio shipping_method_input',
                                'checked' => $finalDefaultMethod == $shippingKey && $finalDefaultOption == $shippingOption,
                                'disabled' => Arr::get($shippingItem, 'disabled'),
                                'data-option' => $shippingOption,
                                'data-auto-selected' => ($autoSelectedMethod == $shippingKey && $autoSelectedOption == $shippingOption) ? 'true' : 'false',
                            ],
                        ]
                    )
                @endforeach
            @endforeach
        </div>
    </div>
@else

    @php
        $sessionCheckoutData = $sessionCheckoutData ?? OrderHelper::getOrderSessionData();
    @endphp

    <div class="no-shipping-methods">
        @if ($sessionCheckoutData && Arr::get($sessionCheckoutData, 'country'))
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                {{ __('No shipping methods were found with your provided shipping information!') }}
            </div>
        @else
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                {{ __('Please fill out all shipping information to view available shipping methods!') }}
            </div>
        @endif
    </div>
@endif

<style>
.modern-shipping-methods-container {
    margin-bottom: 1.5rem;
}

.location-info-banner {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 1px solid #e1f5fe;
    border-radius: 12px;
    padding: 12px 16px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.location-info-content {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #1565c0;
}

.location-info-content i {
    color: #ff6633;
    font-size: 16px;
}

.location-text {
    flex: 1;
}

.location-type {
    color: #6c757d;
    font-weight: normal;
    margin-left: 4px;
}

.auto-selected-badge {
    background: #4caf50;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.shipping-methods-grid {
    display: grid;
    gap: 12px;
}

.no-shipping-methods .alert {
    border-radius: 12px;
    border: none;
    padding: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 500;
}

.no-shipping-methods .alert i {
    font-size: 18px;
}

.no-shipping-methods .alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.no-shipping-methods .alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* Responsive Design */
@media (max-width: 992px) {
    .location-info-banner {
        padding: 10px 14px;
        margin-bottom: 16px;
    }

    .location-info-content {
        font-size: 13px;
        gap: 6px;
    }

    .auto-selected-badge {
        font-size: 10px;
        padding: 2px 6px;
    }

    .shipping-methods-grid {
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .location-info-content {
        flex-wrap: wrap;
    }

    .auto-selected-badge {
        margin-top: 4px;
    }
}
</style>
