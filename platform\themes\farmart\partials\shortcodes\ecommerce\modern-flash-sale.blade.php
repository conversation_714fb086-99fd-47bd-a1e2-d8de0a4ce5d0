<div class="modern-flash-sale py-5">
    <div class="container-xxxl">
        <div class="modern-flash-sale-header">
            <div class="row align-items-center">
                <div class="col-12 col-md-6">
                    <div class="modern-flash-sale-title">
                        <h2 class="mb-0">{!! $shortcode->title ? BaseHelper::clean($shortcode->title) : BaseHelper::clean($flashSale->name) !!}</h2>
                        <div class="flash-sale-subtitle">{{ $shortcode->subtitle ?? __('Limited time offer') }}</div>
                    </div>
                </div>
                <div class="col-12 col-md-6">
                    <div class="modern-countdown-wrapper">
                        <div class="countdown-label">{{ __('Ends in') }}</div>
                        <div class="expire-countdown" data-expire="{{ Carbon\Carbon::now()->diffInSeconds($flashSale->end_date) }}"></div>
                    </div>
                </div>
            </div>
        </div>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                if (jQuery().expireCountdown) {
                    $('.expire-countdown').expireCountdown();
                }
            });
        </script>

        <div class="product-grid-wrapper mt-4">
            <div class="products-grid">
                @foreach ($flashSale->products as $product)
                    <div class="product-inner bg-white">
                        {!! Theme::partial('ecommerce.product-item-grid', compact('product', 'wishlistIds')) !!}
                    </div>
                @endforeach
            </div>
        </div>

        <div class="text-center mt-3">
            <a href="{{ route('public.products') }}" class="btn btn-view-all">
                {{ __('View All Offers') }}
                <i class="fas fa-arrow-right ms-2"></i>
            </a>
        </div>
    </div>
</div>

<style>
    .modern-flash-sale {
       
        background: #ffffff;
        border-radius: 10px;
        overflow: hidden;
        position: relative;
    }

    .modern-flash-sale::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, rgba(255, 102, 51, 0.05) 0%, rgba(255, 153, 102, 0.05) 100%);
        z-index: 0;
    }

    .modern-flash-sale-header {
        background: linear-gradient(135deg, #ff6633 0%, #ff9966 100%);
        padding: 20px;
        border-radius: 8px;
        color: white;
        position: relative;
        z-index: 1;
        box-shadow: 0 4px 15px rgba(255, 102, 51, 0.2);
    }

    .modern-flash-sale-title h2 {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 5px;
        color: white;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }

    .flash-sale-subtitle {
        font-size: 16px;
        opacity: 0.9;
    }

    .modern-countdown-wrapper {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        flex-wrap: wrap;
    }

    .countdown-label {
        font-size: 16px;
        font-weight: 600;
        margin-right: 15px;
    }

    .expire-countdown {
        display: flex;
        align-items: center;
    }

    .expire-countdown .timer {
        background-color: white;
        color: #ff6633;
        border-radius: 6px;
        padding: 8px 12px;
        margin: 0 5px;
        min-width: 60px;
        text-align: center;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .expire-countdown .timer .digits {
        font-size: 22px;
        font-weight: 700;
        display: block;
        line-height: 1.2;
    }

    .expire-countdown .timer .text {
        font-size: 12px;
        color: #666;
        display: block;
    }

    .expire-countdown .divider {
        color: white;
        font-size: 20px;
        font-weight: 700;
    }

    .products-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 1rem;
    }

    @media (max-width: 1400px) {
        .products-grid {
            grid-template-columns: repeat(5, 1fr);
        }
    }

    @media (max-width: 1199px) {
        .products-grid {
            grid-template-columns: repeat(4, 1fr);
        }
    }

    @media (max-width: 1024px) {
        .products-grid {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    @media (max-width: 767px) {
        .products-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;
        }

        .modern-flash-sale-header {
            text-align: center;
        }

        .modern-countdown-wrapper {
            justify-content: center;
            margin-top: 15px;
        }

        .countdown-label {
            margin-right: 0;
            margin-bottom: 10px;
            width: 100%;
            text-align: center;
        }

        .expire-countdown .timer {
            min-width: 50px;
            padding: 6px 8px;
        }

        .expire-countdown .timer .digits {
            font-size: 18px;
        }

        .modern-flash-sale-title h2 {
            font-size: 24px;
        }
    }

    .product-inner {
        width: 100%;
        border-radius: 4px;
        overflow: hidden;
        border: 1px solid #eee;
        transition: box-shadow 0.3s ease;
        display: flex;
        flex-direction: column;
    }

    .product-inner:hover {
        box-shadow: 0 1px 6px rgba(0,0,0,0.1);
    }

    .product-thumbnail {
        position: relative;
        overflow: hidden;
    }

    .product-thumbnail__img {
        width: 100%;
        height: auto;
        transition: transform 0.5s ease;
    }

    .product-inner:hover .product-thumbnail__img {
        transform: scale(1.05);
    }

    .product-wishlist {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 2;
    }

    .product-wishlist .wishlist-add {
        background-color: white;
        border-radius: 50%;
        width: 35px;
        height: 35px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        opacity: 0.8;
        transition: all 0.3s ease;
    }

    .product-wishlist .wishlist-add:hover {
        opacity: 1;
        background-color: #ff6633;
        color: white;
    }

    .product-wishlist .added-to-wishlist {
        background-color: #ff6633;
        color: white;
        opacity: 1;
    }

    .product-details {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
    }

    .product-content-box {
        flex-grow: 1;
    }

    .vendor-info {
        margin-bottom: 5px;
    }

    .vendor-name {
        color: #000080;
        font-size: 12px;
        font-weight: 500;
        background-color: rgba(0, 0, 128, 0.05);
        padding: 2px 8px;
        border-radius: 4px;
        border: 1px solid rgba(0, 0, 128, 0.1);
        display: inline-block;
        transition: all 0.3s ease;
    }

    .vendor-name:hover {
        background-color: rgba(0, 0, 128, 0.1);
        color: #000080;
        text-decoration: none;
    }

    .product-name {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 5px;
        line-height: 1.4;
    }

    .product-name a {
        color: #000;
        transition: color 0.3s ease;
    }

    .product-name a:hover {
        color: #ff6633;
        text-decoration: none;
    }

    .product-price-wrapper {
        background-color: #ff6633;
        padding: 10px 15px;
        margin-top: auto;
        border-radius: 0 0 8px 8px;
    }

    .product-price {
        color: white;
        font-size: 16px;
        font-weight: 600;
    }

    .product-price span,
    .product-price ins,
    .product-price del,
    .product-price .amount {
        color: white !important;
    }

    .product-price ins {
        text-decoration: none;
        font-weight: 700;
    }

    .product-price del {
        opacity: 0.7;
        font-size: 14px;
        margin-left: 5px;
    }

    .ribbons {
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 2;
    }

    .ribbon {
        background-color: #ff6633;
        color: white;
        font-size: 12px;
        font-weight: 600;
        padding: 5px 10px;
        border-radius: 4px;
        display: inline-block;
        margin-right: 5px;
        margin-bottom: 5px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .ribbon.out-stock {
        background-color: #999;
    }

    .btn-view-all {
        background-color: #ff6633;
        color: white;
        border: none;
        padding: 10px 25px;
        border-radius: 6px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 10px rgba(255, 102, 51, 0.2);
    }

    .btn-view-all:hover {
        background-color: #ff5522;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(255, 102, 51, 0.3);
    }
</style>