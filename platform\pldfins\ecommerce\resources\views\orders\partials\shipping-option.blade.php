@php
    $isAutoSelected = Arr::get($attributes, 'data-auto-selected') === 'true';
    $isChecked = Arr::get($attributes, 'checked');
    $isDisabled = Arr::get($attributes, 'disabled');
@endphp

<div class="modern-shipping-option {{ $isChecked ? 'selected' : '' }} {{ $isAutoSelected ? 'auto-selected' : '' }} {{ $isDisabled ? 'disabled' : '' }}">
    {!! Form::radio(Arr::get($attributes, 'name'), $shippingKey, $isChecked, $attributes) !!}
    <label for="{{ Arr::get($attributes, 'id') }}" class="shipping-option-label">
        <div class="shipping-option-content">
            <div class="shipping-option-header">
                <div class="shipping-option-main">
                    @if ($image = Arr::get($shippingItem, 'image'))
                        <div class="shipping-option-image">
                            <img
                                src="{{ $image }}"
                                alt="{{ $shippingItem['name'] }}"
                                loading="lazy"
                            >
                        </div>
                    @endif
                    <div class="shipping-option-info">
                        <div class="shipping-option-name">
                            {{ $shippingItem['name'] }}
                            @if ($isAutoSelected)
                                <span class="recommended-badge">{{ __('Recommended') }}</span>
                            @endif
                        </div>
                        <div class="shipping-option-price">
                            @if ($shippingItem['price'] > 0)
                                <span class="price-amount">{{ format_price($shippingItem['price']) }}</span>
                            @else
                                <span class="free-shipping">{{ __('Free shipping') }}</span>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="shipping-option-radio">
                    <div class="radio-indicator"></div>
                </div>
            </div>

            @if ($description = Arr::get($shippingItem, 'description'))
                <div class="shipping-option-description">
                    {!! BaseHelper::clean($description) !!}
                </div>
            @endif

            @if ($errorMessage = Arr::get($shippingItem, 'error_message'))
                <div class="shipping-option-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    {!! BaseHelper::clean($errorMessage) !!}
                </div>
            @endif
        </div>
    </label>
</div>

<style>
.modern-shipping-option {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    background: #fff;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.modern-shipping-option:hover {
    border-color: #ff6633;
    box-shadow: 0 4px 12px rgba(255, 102, 51, 0.15);
    transform: translateY(-2px);
}

.modern-shipping-option.selected {
    border-color: #ff6633;
    background: linear-gradient(135deg, #fff5f2 0%, #fff 100%);
    box-shadow: 0 4px 16px rgba(255, 102, 51, 0.2);
}

.modern-shipping-option.auto-selected {
    border-color: #4caf50;
    background: linear-gradient(135deg, #f1f8e9 0%, #fff 100%);
}

.modern-shipping-option.auto-selected:hover,
.modern-shipping-option.auto-selected.selected {
    border-color: #4caf50;
    box-shadow: 0 4px 16px rgba(76, 175, 80, 0.2);
}

.modern-shipping-option.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #f8f9fa;
}

.modern-shipping-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.shipping-option-label {
    display: block;
    padding: 16px;
    margin: 0;
    cursor: pointer;
    width: 100%;
}

.shipping-option-content {
    width: 100%;
}

.shipping-option-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
}

.shipping-option-main {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
}

.shipping-option-image {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.shipping-option-image img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.shipping-option-info {
    flex: 1;
    min-width: 0;
}

.shipping-option-name {
    font-weight: 600;
    font-size: 15px;
    color: #2c3e50;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.recommended-badge {
    background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.shipping-option-price {
    font-size: 14px;
}

.price-amount {
    font-weight: 700;
    color: #ff6633;
    font-size: 16px;
}

.free-shipping {
    font-weight: 700;
    color: #4caf50;
    font-size: 14px;
}

.shipping-option-radio {
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    border: 2px solid #dee2e6;
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;
}

.modern-shipping-option.selected .shipping-option-radio {
    border-color: #ff6633;
    background: #ff6633;
}

.modern-shipping-option.auto-selected .shipping-option-radio {
    border-color: #4caf50;
}

.modern-shipping-option.auto-selected.selected .shipping-option-radio {
    background: #4caf50;
}

.radio-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: white;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modern-shipping-option.selected .radio-indicator {
    opacity: 1;
}

.shipping-option-description {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f1f3f4;
    font-size: 13px;
    color: #6c757d;
    line-height: 1.4;
}

.shipping-option-error {
    margin-top: 12px;
    padding: 8px 12px;
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 6px;
    color: #c53030;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.shipping-option-error i {
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 992px) {
    .shipping-option-label {
        padding: 14px;
    }

    .shipping-option-header {
        gap: 10px;
    }

    .shipping-option-main {
        gap: 10px;
    }

    .shipping-option-image {
        width: 40px;
        height: 40px;
    }

    .shipping-option-name {
        font-size: 14px;
    }

    .price-amount {
        font-size: 15px;
    }
}

@media (max-width: 768px) {
    .shipping-option-label {
        padding: 12px;
    }

    .shipping-option-name {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .recommended-badge {
        font-size: 9px;
        padding: 1px 6px;
    }
}
</style>
