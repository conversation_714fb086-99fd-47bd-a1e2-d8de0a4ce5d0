<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'name',
    'label' => null,
    'description' => null,
    'paymentName' => null,
    'supportedCurrencies' => [],
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'name',
    'label' => null,
    'description' => null,
    'paymentName' => null,
    'supportedCurrencies' => [],
]); ?>
<?php foreach (array_filter(([
    'name',
    'label' => null,
    'description' => null,
    'paymentName' => null,
    'supportedCurrencies' => [],
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php
    $isSelected = PaymentMethods::getSelectingMethod() === $name;
    $id = sprintf('payment-%s', $name);
?>

<li class="list-group-item payment-method-item">
    <input
        class="magic-radio js_payment_method"
        id="<?php echo e($id); ?>"
        name="payment_method"
        type="radio"
        value="<?php echo e($name); ?>"
        <?php if($isSelected): echo 'checked'; endif; ?>
    >
    <label for="<?php echo e($id); ?>">
        <?php echo e($paymentLabel = ($label ?: get_payment_setting('name', $name) ?: setting('payment_' . $name . '_name') ?: $paymentName)); ?>

    </label>

    <div class="<?php echo \Illuminate\Support\Arr::toCssClasses(['payment_collapse_wrap collapse mt-1', 'show' => $isSelected]); ?>">
        <p><?php echo BaseHelper::clean($description ?: get_payment_setting('description', $name) ?: setting('payment_' . $name . '_description')); ?></p>

        <?php echo e($slot); ?>


        <?php echo apply_filters('payment_method_display_body', null, $name, $paymentLabel); ?>


        <?php if(
            ! empty($supportedCurrencies)
            && ! in_array(get_application_currency()->title, $supportedCurrencies)
            && ! get_application_currency()->replicate()->newQuery()->whereIn('title', $supportedCurrencies)->exists()
        ): ?>
            <?php
                $currencies = get_all_currencies()->filter(fn ($item) => in_array($item->title, $supportedCurrencies));
            ?>

            <div class="alert alert-warning mt-3">
                <?php echo e(__(":name doesn't support :currency. List of currencies supported by :name: :currencies.", ['name' => $paymentName, 'currency' => get_application_currency()->title, 'currencies' => implode(', ', $supportedCurrencies)])); ?>


                <?php echo e($currencyNotSupportedMessage ?? ''); ?>


                <?php if($currencies->isNotEmpty()): ?>
                    <div>
                        <?php echo e(__('Please switch currency to any supported currency')); ?>:&nbsp;&nbsp;
                        <?php $__currentLoopData = $currencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <a
                                href="<?php echo e(route('public.change-currency', $currency->title)); ?>"
                                class="<?php echo \Illuminate\Support\Arr::toCssClasses(['active' => get_application_currency_id() === $currency->getKey()]); ?>"
                            >
                                <?php echo e($currency->title); ?>

                            </a>
                            <?php if(!$loop->last): ?>
                                &nbsp; | &nbsp;
                            <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <?php if($logo = get_payment_setting('logo', $name)): ?>
        <div class="payment-method-logo">
            <?php echo e(RvMedia::image($logo)); ?>

        </div>
    <?php endif; ?>
</li>
<?php /**PATH C:\Users\<USER>\Desktop\uu\platform/plugins/payment/resources/views/components/payment-method.blade.php ENDPATH**/ ?>